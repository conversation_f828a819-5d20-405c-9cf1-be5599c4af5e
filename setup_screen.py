"""
Tela de SETUP para configuração de consultas de busca e configurações de salvamento.
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QLineEdit, QFrame, QScrollArea, QRadioButton,
                            QButtonGroup, QFileDialog, QMessageBox, QCheckBox,
                            QDialog, QListWidget, QListWidgetItem)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon
import config_manager

from desktop_theme import (
    PRIMARY_COLOR, SECONDARY_COLOR, CARD_COLOR, TEXT_PRIMARY, SUCCESS_COLOR, ERROR_COLOR,
    style_heading_label, style_subheading_label, style_body_label,
    create_card_frame, style_primary_button, style_secondary_button
)

from ai_integration import get_search_suggestions, get_profession_suggestions, get_location_suggestions

class SuggestionDialog(QDialog):
    """Diálogo para exibir sugestões de busca."""
    def __init__(self, suggestions, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Sugestões de Busca")
        self.setMinimumWidth(400)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {CARD_COLOR};
                border-radius: 8px;
            }}
        """)

        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Clique em uma sugestão para usá-la:")
        style_subheading_label(title)
        layout.addWidget(title)

        # Lista de sugestões
        self.suggestion_list = QListWidget()
        self.suggestion_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #EEEEEE;
            }}
            QListWidget::item:selected {{
                background-color: {PRIMARY_COLOR};
                color: white;
            }}
            QListWidget::item:hover:!selected {{
                background-color: #E3F2FD;
            }}
        """)

        for suggestion in suggestions:
            item = QListWidgetItem(suggestion)
            self.suggestion_list.addItem(item)

        self.suggestion_list.itemDoubleClicked.connect(self.accept)
        layout.addWidget(self.suggestion_list)

        # Botão de fechar
        close_button = QPushButton("Fechar")
        style_secondary_button(close_button)
        close_button.clicked.connect(self.reject)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

    def get_selected_suggestion(self):
        """Retorna a sugestão selecionada."""
        items = self.suggestion_list.selectedItems()
        if items:
            return items[0].text()
        return None

class SetupScreen(QWidget):
    """Tela de SETUP para configuração de consultas e salvamento."""

    # Sinal emitido quando as configurações são salvas
    config_saved = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.query_fields = []

        # Carregar configurações
        self.config = config_manager.load_config()

        self.initUI()
        self.load_saved_queries()

    def initUI(self):
        """Inicializa a interface da tela SETUP."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Título da tela
        title_label = QLabel("SETUP - Configuração de Buscas Automatizadas")
        style_heading_label(title_label)
        layout.addWidget(title_label)

        # Descrição
        description = QLabel("Configure até 17 consultas de busca e as configurações de salvamento.")
        style_body_label(description)
        layout.addWidget(description)

        # Card para consultas de busca
        queries_card = create_card_frame()
        queries_layout = QVBoxLayout(queries_card)

        # Título do card
        queries_title = QLabel("Consultas de Busca")
        style_subheading_label(queries_title)
        queries_layout.addWidget(queries_title)

        # Área de rolagem para as consultas
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {CARD_COLOR};
                border: none;
            }}
        """)

        # Container para os campos de consulta
        scroll_content = QWidget()
        self.queries_container = QVBoxLayout(scroll_content)
        self.queries_container.setSpacing(10)

        scroll_area.setWidget(scroll_content)
        queries_layout.addWidget(scroll_area)

        # Botão para adicionar nova consulta
        add_query_button = QPushButton("Adicionar Nova Consulta")
        add_query_button.setIcon(QIcon.fromTheme("list-add"))
        style_primary_button(add_query_button)
        add_query_button.clicked.connect(self.add_query_field)

        queries_layout.addWidget(add_query_button)
        layout.addWidget(queries_card)

        # Adicionar campos de consulta iniciais (17 como solicitado)
        for i in range(17):
            self.add_query_field()

        # Card para configurações de salvamento
        config_card = create_card_frame()
        config_layout = QVBoxLayout(config_card)

        # Título do card
        config_title = QLabel("Configurações de Salvamento")
        style_subheading_label(config_title)
        config_layout.addWidget(config_title)

        # Diretório para salvar
        save_dir_layout = QHBoxLayout()
        save_dir_label = QLabel("Diretório para Salvar:")
        style_body_label(save_dir_label)
        self.save_dir = QLineEdit()
        self.save_dir.setReadOnly(True)
        self.save_dir.setStyleSheet(f"""
            QLineEdit {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                padding: 8px;
            }}
        """)
        self.save_dir.setText(self.config.get("default_save_dir", ""))

        browse_button = QPushButton("Selecionar")
        browse_button.setIcon(QIcon.fromTheme("folder-open"))
        style_secondary_button(browse_button)
        browse_button.clicked.connect(self.select_save_directory)

        save_dir_layout.addWidget(save_dir_label)
        save_dir_layout.addWidget(self.save_dir)
        save_dir_layout.addWidget(browse_button)
        config_layout.addLayout(save_dir_layout)

        # Formato do arquivo
        format_layout = QVBoxLayout()
        format_label = QLabel("Formato do arquivo:")
        style_body_label(format_label)
        format_layout.addWidget(format_label)

        radio_layout = QHBoxLayout()
        self.excel_radio = QRadioButton("Excel")
        self.csv_radio = QRadioButton("CSV")

        # Definir o formato padrão com base nas configurações
        if self.config.get("default_export_format", "excel") == "excel":
            self.excel_radio.setChecked(True)
        else:
            self.csv_radio.setChecked(True)

        radio_layout.addWidget(self.excel_radio)
        radio_layout.addWidget(self.csv_radio)
        radio_layout.addStretch()

        format_layout.addLayout(radio_layout)
        config_layout.addLayout(format_layout)

        # Opção de modo headless
        headless_layout = QHBoxLayout()
        headless_label = QLabel("Modo de Execução:")
        style_body_label(headless_label)
        self.headless_checkbox = QCheckBox("Executar em segundo plano (headless)")
        self.headless_checkbox.setChecked(self.config.get("headless_mode", True))
        self.headless_checkbox.setToolTip("Quando ativado, o navegador funciona em segundo plano sem exibir janela")

        headless_layout.addWidget(headless_label)
        headless_layout.addWidget(self.headless_checkbox)
        headless_layout.addStretch()

        config_layout.addLayout(headless_layout)
        layout.addWidget(config_card)

        # Botões de ação
        buttons_layout = QHBoxLayout()

        # Botão Salvar
        save_button = QPushButton("Salvar Configurações")
        save_button.setIcon(QIcon.fromTheme("document-save"))
        style_primary_button(save_button)
        save_button.clicked.connect(self.save_config)

        # Botão Voltar
        back_button = QPushButton("← Voltar")
        back_button.setIcon(QIcon.fromTheme("go-previous"))
        style_secondary_button(back_button)
        back_button.clicked.connect(self.go_back)

        buttons_layout.addWidget(back_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)

        layout.addWidget(QWidget())  # Spacer
        layout.addLayout(buttons_layout)

    def add_query_field(self, query=""):
        """Adiciona um campo de consulta de busca."""
        # Container para o campo e botões
        container = QFrame()
        container.setStyleSheet(f"""
            QFrame {{
                background-color: #F5F5F5;
                border-radius: 4px;
                padding: 5px;
            }}
        """)

        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(5, 5, 5, 5)

        # Campo de texto
        query_field = QLineEdit(query)
        query_field.setPlaceholderText(f"Busca {len(self.query_fields) + 1}: Ex.: 'DENTISTA + RJ'")
        query_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                padding: 8px;
            }}
            QLineEdit:focus {{
                border: 2px solid {PRIMARY_COLOR};
            }}
        """)

        # Botão de sugestões
        suggestion_button = QPushButton()
        suggestion_button.setIcon(QIcon.fromTheme("dialog-information"))
        suggestion_button.setToolTip("Obter sugestões de busca")
        suggestion_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {SECONDARY_COLOR};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                min-width: 30px;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: #2BC5A8;
            }}
        """)
        suggestion_button.clicked.connect(lambda: self.get_suggestions(query_field))

        # Botão de remoção
        remove_button = QPushButton()
        remove_button.setIcon(QIcon.fromTheme("edit-delete"))
        remove_button.setToolTip("Remover")
        remove_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ERROR_COLOR};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                min-width: 30px;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: #D32F2F;
            }}
        """)
        remove_button.clicked.connect(lambda: self.remove_query_field(container))

        container_layout.addWidget(query_field)
        container_layout.addWidget(suggestion_button)
        container_layout.addWidget(remove_button)

        self.queries_container.addWidget(container)
        self.query_fields.append(query_field)

    def remove_query_field(self, container):
        """Remove um campo de consulta."""
        # Encontrar o campo correspondente
        for i, field in enumerate(self.query_fields):
            if field.parent() == container:
                self.query_fields.pop(i)
                break

        # Remover o container da interface
        self.queries_container.removeWidget(container)
        container.deleteLater()

        # Atualizar placeholders
        for i, field in enumerate(self.query_fields):
            field.setPlaceholderText(f"Busca {i + 1}: Ex.: 'DENTISTA + RJ'")

    def get_suggestions(self, query_field):
        """Obtém sugestões de busca usando GPT-4 Mini."""
        try:
            if not query_field.text():
                # Se o campo estiver vazio, obter sugestões de profissões e localizações
                professions = get_profession_suggestions(5)
                locations = get_location_suggestions(5)
                # Combinar profissões e localizações
                suggestions = [f"{prof} + {loc}" for prof, loc in zip(professions[:5], locations[:5])]
            else:
                # Se o campo tiver texto, obter sugestões baseadas no texto
                suggestions = get_search_suggestions(query_field.text(), 5)

            # Exibir diálogo de sugestões
            dialog = SuggestionDialog(suggestions, self)
            if dialog.exec():
                selected = dialog.get_selected_suggestion()
                if selected:
                    query_field.setText(selected)

        except Exception as e:
            QMessageBox.warning(self, "Erro", f"Erro ao obter sugestões: {str(e)}")

    def select_save_directory(self):
        """Seleciona o diretório para salvar os resultados."""
        # Usar o diretório padrão das configurações como ponto de partida
        default_dir = self.config.get("default_save_dir", "")

        directory = QFileDialog.getExistingDirectory(
            self,
            "Selecionar Diretório",
            default_dir
        )

        if directory:
            self.save_dir.setText(directory)

    def save_config(self):
        """Salva as configurações."""
        # Validar entradas
        if not self.save_dir.text():
            QMessageBox.warning(self, "Erro", "Por favor, selecione um diretório para salvar os resultados.")
            return

        # Obter consultas de busca
        queries = [field.text() for field in self.query_fields if field.text()]
        if not queries:
            QMessageBox.warning(self, "Erro", "Por favor, adicione pelo menos uma consulta de busca.")
            return

        # Salvar configurações
        config = {
            "search_queries": queries,
            "default_save_dir": self.save_dir.text(),
            "default_export_format": "excel" if self.excel_radio.isChecked() else "csv",
            "headless_mode": self.headless_checkbox.isChecked()
        }

        config_manager.save_config(config)

        QMessageBox.information(
            self,
            "Sucesso",
            f"Configurações salvas com sucesso!\n\n"
            f"• {len(queries)} consultas de busca configuradas\n"
            f"• Diretório: {self.save_dir.text()}\n"
            f"• Formato: {'Excel' if self.excel_radio.isChecked() else 'CSV'}\n"
            f"• Modo: {'Headless' if self.headless_checkbox.isChecked() else 'Visual'}"
        )

        # Emitir sinal de configuração salva
        self.config_saved.emit()

    def load_saved_queries(self):
        """Carrega as consultas salvas nas configurações."""
        saved_queries = self.config.get("search_queries", [])

        # Preencher os campos com as consultas salvas
        for i, query in enumerate(saved_queries):
            if i < len(self.query_fields):
                self.query_fields[i].setText(query)

    def go_back(self):
        """Volta para a tela anterior."""
        if self.parent:
            # Se for uma janela modal, fechar
            if hasattr(self, 'close'):
                self.close()
            # Se for um widget em stack, voltar
            elif hasattr(self.parent, 'stacked_widget'):
                self.parent.stacked_widget.setCurrentIndex(1)  # Voltar para busca automatizada
