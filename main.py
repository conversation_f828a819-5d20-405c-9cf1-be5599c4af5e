import sys
import os
from PyQt6.QtWidgets import (Q<PERSON><PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QSpinBox, QCheckBox, QPushButton,
                             QFileDialog, QTreeWidget, QTreeWidgetItem, QProgressBar,
                             QMessageBox, QFrame, QDialog, QGroupBox, QTextEdit,
                             QStackedWidget, QRadioButton, QButtonGroup, QLineEdit,
                             QTableWidget, QTableWidgetItem, QHeaderView, QSplitter,
                             QComboBox, QScrollArea)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt6.QtGui import QIcon, QPixmap, QFont, QPalette, QColor

# Importar diálogos de configurações e ajuda
from settings_dialog import SettingsDialog
from help_dialog import HelpDialog
import config_manager
import pandas as pd
import time
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
import urllib.parse
from pathlib import Path
import json
import schedule
from dataclasses import dataclass, asdict, field

# Importar a aba de busca automatizada
from automated_search_tab import AutomatedSearchTab

# Importar tema e estilos
from desktop_theme import (
    get_application_palette, get_application_stylesheet,
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BACKGROUND_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR, WARNING_COLOR, INFO_COLOR,
    style_heading_label, style_subheading_label, style_body_label, style_caption_label,
    create_card_frame, style_primary_button, style_secondary_button, style_accent_button,
    get_logo_pixmap, FONT_FAMILY
)

PASTA_ICONES = Path(__file__).parent / "icons"

@dataclass
class Business:
    """Representa um negócio extraído do Google Maps."""
    name: str = ""
    address: str = ""
    phone_number: str = ""
    website: str = ""
    rating: str = ""
    reviews_count: str = ""
    category: str = ""
    timetable: str = ""
    latitude: str = ""
    longitude: str = ""
    plus_code: str = ""
    place_id: str = ""

@dataclass
class BusinessList:
    """Armazena uma lista de objetos Business e permite salvar os resultados em Excel ou CSV."""
    business_list: list[Business] = field(default_factory=list)

    def dataframe(self):
        """Transforma a lista de negócios em um DataFrame do pandas."""
        return pd.json_normalize((asdict(business) for business in self.business_list), sep="_")

    def save_to_excel(self, filename, save_dir):
        """Salva o DataFrame em um arquivo Excel."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.xlsx'
            self.dataframe().to_excel(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

    def save_to_csv(self, filename, save_dir):
        """Salva o DataFrame em um arquivo CSV."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.csv'
            self.dataframe().to_csv(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

def move_map(navegador, direction='right'):
    """
    Move o mapa na direção especificada.
    direction: 'right', 'left', 'up', 'down'
    """
    # Primeiro clica no mapa para garantir que está focado
    map_element = navegador.find_element(By.CLASS_NAME, 'widget-scene')
    action = ActionChains(navegador)
    action.move_to_element(map_element).click().perform()
    time.sleep(1)

    # Define quantas vezes pressionar a tecla de seta
    presses = 10

    # Mapeia a direção para a tecla correspondente
    key_map = {
        'right': Keys.ARROW_RIGHT,
        'left': Keys.ARROW_LEFT,
        'up': Keys.ARROW_UP,
        'down': Keys.ARROW_DOWN
    }

    # Pressiona a tecla várias vezes para mover o mapa
    key = key_map.get(direction, Keys.ARROW_RIGHT)
    for _ in range(presses):
        action.send_keys(key).perform()
        time.sleep(0.1)

    # Aguarda um pouco para o mapa carregar
    time.sleep(3)

class GoogleMapsScraperThread(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    continue_query = pyqtSignal(str, bool)

    def __init__(self, search_for, total, location, save_dir, file_format, headless_mode=True):
        super().__init__()
        self.search_for = search_for
        self.total = total
        self.location = location
        self.save_dir = save_dir
        self.file_format = file_format
        self.headless_mode = headless_mode
        self.continue_response = None

    def run(self):
        try:
            # Configura o Chrome com as opções adequadas
            chrome_options = Options()

            # Configurações para modo headless (se ativado)
            if self.headless_mode:
                self.status_updated.emit("[INFO] Iniciando Chrome em modo headless (segundo plano)...")
                chrome_options.add_argument("--headless=new")  # Novo modo headless do Chrome
                chrome_options.add_argument("--window-size=1920,1080")  # Tamanho da janela virtual
            else:
                self.status_updated.emit("[INFO] Iniciando Chrome em modo visível...")
                chrome_options.add_argument("--start-maximized")  # Maximizar janela

            # Configurações para evitar problemas com GPU e melhorar estabilidade
            chrome_options.add_argument("--disable-gpu")  # Desativa aceleração por hardware
            chrome_options.add_argument("--disable-dev-shm-usage")  # Evita problemas com memória compartilhada
            chrome_options.add_argument("--no-sandbox")  # Desativa o sandbox para evitar problemas
            chrome_options.add_argument("--disable-extensions")  # Desativa extensões
            chrome_options.add_argument("--disable-software-rasterizer")  # Evita problemas com renderização
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")  # Desativa o compositor de exibição
            chrome_options.add_argument("--disable-features=UseOzonePlatform")  # Desativa a plataforma Ozone
            chrome_options.add_argument("--disable-features=TensorFlowLite")  # Desativa TensorFlow Lite
            chrome_options.add_argument("--disable-features=TensorFlowLiteDelegate")  # Desativa delegados do TensorFlow Lite

            # Configurações adicionais para melhorar desempenho em modo headless
            chrome_options.add_argument("--disable-notifications")  # Desativa notificações
            chrome_options.add_argument("--disable-infobars")  # Desativa barras de informação
            chrome_options.add_argument("--mute-audio")  # Desativa áudio
            chrome_options.add_argument("--disable-popup-blocking")  # Permite popups (necessário para alguns sites)
            chrome_options.add_argument("--log-level=3")  # Minimiza logs

            # Configurar user-agent para evitar detecção de headless
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

            self.status_updated.emit("[INFO] Abrindo Google Maps...")
            navegador.get("https://www.google.com.br/maps")
            time.sleep(3)

            # Insere a localização e executa a busca inicial
            self.status_updated.emit(f"[INFO] Buscando localização: {self.location}...")
            navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(self.location)
            time.sleep(2)
            navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
            time.sleep(15)
            navegador.find_element(By.XPATH, '//*[@id="searchbox"]/div[2]/button').click()
            time.sleep(5)

            # Realiza a busca do termo combinado (negócio + localização)
            self.status_updated.emit(f"[INFO] Buscando termo: {self.search_for}...")
            navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(self.search_for)
            navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
            time.sleep(10)

            business_list = BusinessList()
            i = 0
            move_count = 0
            max_moves = 4  # Número máximo de movimentos do mapa

            while i < self.total and move_count < max_moves:
                previously_counted = 0
                stuck_count = 0

                # Número mínimo de elementos a encontrar antes de começar a processar
                # Isso ajuda a garantir que temos elementos suficientes para escolher
                min_elements_to_find = min(self.total * 2, 20)  # No máximo 20 elementos ou 2x o número solicitado

                # Rolar apenas até encontrar elementos suficientes ou ficar preso
                while True:
                    list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                    if not list_elem:
                        self.status_updated.emit("[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região.")
                        navegador.quit()
                        self.finished_signal.emit("[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região.")
                        return

                    current_count = len(list_elem)

                    # Se já temos elementos suficientes, podemos parar de rolar
                    if current_count >= min_elements_to_find:
                        self.status_updated.emit(f"[INFO] Encontrados {current_count} elementos, processando os primeiros {self.total}...")
                        break

                    # Se já temos elementos suficientes para o que o usuário pediu, podemos parar de rolar
                    if current_count >= self.total:
                        self.status_updated.emit(f"[INFO] Encontrados {current_count} elementos, processando...")
                        break

                    action = ActionChains(navegador)
                    try:
                        action.move_to_element(list_elem[-1]).perform()
                    except Exception:
                        list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                        action.move_to_element(list_elem[-1]).perform()
                    time.sleep(3)  # Reduzido para 3 segundos

                    scroll_origin = ScrollOrigin.from_element(list_elem[-1])
                    action.scroll_from_origin(scroll_origin, 0, 1200).perform()
                    time.sleep(5)  # Reduzido para 5 segundos
                    action.scroll_from_origin(scroll_origin, 0, 250).perform()

                    current_count = len(list_elem)
                    if current_count == previously_counted:
                        stuck_count += 1
                        if stuck_count >= 2:  # Reduzido para 2 tentativas
                            self.status_updated.emit(f"[INFO] Não foi possível encontrar mais elementos, processando os {current_count} encontrados...")
                            break
                    else:
                        stuck_count = 0
                        previously_counted = current_count

                # Processa os elementos encontrados
                list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                for element in list_elem[i:]:
                    if i >= self.total:
                        break

                    try:
                        time.sleep(2)
                        navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                        time.sleep(2)
                        try:
                            element.click()
                        except Exception as click_err:
                            error_msg = f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}"
                            self.status_updated.emit(error_msg)
                            i += 1
                            continue
                        time.sleep(6)

                        # XPaths para extração dos dados
                        name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
                        address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
                        website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
                        phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'

                        # Extrair dados
                        business = Business()

                        try:
                            business.name = navegador.find_element(By.XPATH, name_xpath).text
                        except NoSuchElementException:
                            business.name = "Não disponível"

                        try:
                            business.address = navegador.find_element(By.XPATH, address_xpath).text
                        except NoSuchElementException:
                            business.address = "Não disponível"

                        try:
                            business.website = navegador.find_element(By.XPATH, website_xpath).text
                        except NoSuchElementException:
                            business.website = "Não disponível"

                        try:
                            business.phone_number = navegador.find_element(By.XPATH, phone_number_xpath).text
                        except NoSuchElementException:
                            business.phone_number = "Não disponível"

                        # Extrair avaliação e número de reviews
                        try:
                            rating_elem = navegador.find_element(By.CSS_SELECTOR, 'div.F7nice')
                            rating_text = rating_elem.text
                            if rating_text:
                                parts = rating_text.split('\n')
                                if len(parts) >= 2:
                                    business.rating = parts[0]
                                    business.reviews_count = parts[1].replace('(', '').replace(')', '')
                        except NoSuchElementException:
                            business.rating = "Não disponível"
                            business.reviews_count = "Não disponível"

                        # Extrair categoria
                        try:
                            category_elem = navegador.find_element(By.CSS_SELECTOR, 'button[jsaction="pane.rating.category"]')
                            business.category = category_elem.text
                        except NoSuchElementException:
                            business.category = "Não disponível"

                        # Extrair horário de funcionamento
                        try:
                            hours_button = navegador.find_element(By.CSS_SELECTOR, 'div[data-tooltip="Horário de funcionamento"]')
                            hours_button.click()
                            time.sleep(1)
                            hours_text = navegador.find_element(By.CSS_SELECTOR, 'div.OMl5r').text
                            business.timetable = hours_text
                        except Exception:
                            business.timetable = "Não disponível"

                        # Extrair coordenadas da URL
                        try:
                            current_url = navegador.current_url
                            if '@' in current_url:
                                coords = current_url.split('@')[1].split(',')
                                if len(coords) >= 2:
                                    business.latitude = coords[0]
                                    business.longitude = coords[1]
                        except Exception:
                            business.latitude = "Não disponível"
                            business.longitude = "Não disponível"

                        # Extrair plus code
                        try:
                            plus_code_elem = navegador.find_element(By.CSS_SELECTOR, 'button[data-item-id="oloc"]')
                            business.plus_code = plus_code_elem.text
                        except NoSuchElementException:
                            business.plus_code = "Não disponível"

                        # Extrair place_id da URL
                        try:
                            if 'place/' in navegador.current_url:
                                place_id = navegador.current_url.split('place/')[1].split('/')[0]
                                business.place_id = place_id
                        except Exception:
                            business.place_id = "Não disponível"

                        business_list.business_list.append(business)
                        time.sleep(3)
                        i += 1

                        porcentagem = (i / self.total) * 100
                        self.progress_updated.emit(int(porcentagem))
                        self.status_updated.emit(f"[PROGRESSO] Processando registro {i} de {self.total} ({porcentagem:.1f}%)")

                    except StaleElementReferenceException:
                        error_msg = f"[AVISO] Elemento {i} está desatualizado, tentando próximo registro..."
                        self.status_updated.emit(error_msg)
                        i += 1
                        continue

                # Se ainda não atingimos o total desejado, pergunta se quer continuar
                if i < self.total:
                    msg = f"[INFO] Encontrados {i} de {self.total} registros desejados.\n[AÇÃO] Deseja continuar a busca movendo o mapa para encontrar mais resultados? (S/N)"
                    self.status_updated.emit(msg)
                    self.continue_query.emit(msg, True)

                    # Aguardar resposta
                    while self.continue_response is None:
                        time.sleep(0.5)

                    if self.continue_response.upper() == 'S':
                        # Move o mapa em diferentes direções a cada iteração
                        directions = ['right', 'down', 'left', 'up']
                        move_map(navegador, directions[move_count % len(directions)])
                        move_count += 1
                        time.sleep(5)  # Aguarda o mapa carregar
                        self.continue_response = None  # Resetar para próxima iteração
                    else:
                        break
                else:
                    break

            # Finaliza a extração e salva automaticamente os dados
            updated_string = self.search_for.replace(" ", "_")
            result = None
            if self.file_format.lower() == "excel":
                result = business_list.save_to_excel(f'maps_data_{updated_string}', self.save_dir)
            elif self.file_format.lower() == "csv":
                result = business_list.save_to_csv(f'maps_data_{updated_string}', self.save_dir)
            else:
                result = "Formato de arquivo inválido."

            navegador.quit()
            self.status_updated.emit(result)
            self.finished_signal.emit(result)

        except Exception as e:
            error_msg = f"[ERRO] Ocorreu um erro durante a extração: {str(e)}"
            self.status_updated.emit(error_msg)
            self.finished_signal.emit(error_msg)
            try:
                navegador.quit()
            except:
                pass

class SendMessagesThread(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(int)

    MENSAGENS = {
        'A': "Olá {}, tudo bem? Vi que você...",  # 50% dos contatos
        'B': "Oi {}, posso te fazer uma pergunta rápida?",  # 25% dos contatos
        'C': "Oi {}, tenho uma dica que pode te ajudar..."  # 25% dos contatos
    }

    def __init__(self, df, driver_setup):
        super().__init__()
        self.df = df
        self.driver_setup = driver_setup
        self.batch_size = random.randint(5, 7)  # Tamanho do lote aleatório entre 5-7
        self.distribuir_mensagens()

    def distribuir_mensagens(self):
        # Distribuir os tipos de mensagem conforme os percentuais
        n_total = len(self.df)
        n_a = int(n_total * 0.5)  # 50% para A
        n_b = int(n_total * 0.25)  # 25% para B
        # O restante vai para C

        tipos = ['A'] * n_a + ['B'] * n_b + ['C'] * (n_total - n_a - n_b)
        random.shuffle(tipos)  # Embaralhar os tipos
        self.df['tipo_mensagem'] = tipos

    def run(self):
        driver = None
        messages_sent = 0
        total_messages = len(self.df)
        self.df['horario_envio'] = None  # Coluna para registrar horário

        try:
            # Inicializar o driver e o WhatsApp Web
            self.status_updated.emit("🔄 Iniciando navegador...")
            driver = self.driver_setup()
            if not driver:
                self.status_updated.emit("❌ Falha ao inicializar o driver do Chrome")
                return

            self.status_updated.emit("🔄 Inicializando WhatsApp Web...")
            driver.get("https://web.whatsapp.com")

            # Aguardar elementos principais carregarem
            WebDriverWait(driver, 30).until(
                lambda x: x.find_element(By.XPATH, "//*[@id='app']")
            )
            self.status_updated.emit("✅ WhatsApp Web inicializado")
            time.sleep(2)  # Pequena pausa adicional

            # Processar em lotes
            for i in range(0, total_messages, self.batch_size):
                batch = self.df.iloc[i:i + self.batch_size]

                for _, row in batch.iterrows():
                    try:
                        phone = str(row['telefone'])
                        tipo = row['tipo_mensagem']

                        # Validar tipo de mensagem
                        if tipo not in self.MENSAGENS:
                            self.status_updated.emit(f"❌ Tipo de mensagem inválido para {phone}: {tipo}")
                            continue

                        # Formatar mensagem
                        try:
                            message_template = str(self.MENSAGENS[tipo])
                            if not message_template:
                                raise ValueError("Template vazio")

                            last_digits = phone[-4:] if len(phone) >= 4 else phone
                            message = message_template.format(last_digits)

                            if not message or not isinstance(message, str):
                                raise ValueError("Mensagem inválida")
                        except Exception as e:
                            self.status_updated.emit(f"❌ Erro ao formatar mensagem para {phone}: {str(e)}")
                            continue

                        # Tentar enviar com retentativas
                        self.status_updated.emit(f"📱 Enviando mensagem para {phone}...")
                        horario = datetime.now()
                        retry_count = 0
                        max_retries = 3
                        success = False

                        while retry_count < max_retries and not success:
                            try:
                                if retry_count > 0:
                                    self.status_updated.emit(f"⚠️ Tentativa {retry_count + 1} para {phone}...")
                                    time.sleep(30)

                                success = self.send_invite_message(driver, phone, message)

                                if success:
                                    messages_sent += 1
                                    progress = int((messages_sent / total_messages) * 100)
                                    self.progress_updated.emit(progress)
                                    self.df.at[row.name, 'horario_envio'] = horario
                                    self.status_updated.emit(f"✅ Mensagem enviada para {phone}")

                                    # Intervalo entre mensagens
                                    base_interval = random.randint(8 * 60, 12 * 60)
                                    variation = random.randint(-120, 120)
                                    interval = base_interval + variation
                                    self.status_updated.emit(f"⏳ Aguardando {interval//60} minutos...")
                                    time.sleep(interval)
                                else:
                                    retry_count += 1
                            except Exception as e:
                                self.status_updated.emit(f"⚠️ Erro na tentativa {retry_count + 1}: {str(e)}")
                                retry_count += 1
                                time.sleep(30)

                            if retry_count == max_retries:
                                self.status_updated.emit(f"❌ Falha após {max_retries} tentativas para {phone}")

                    except Exception as e:
                        self.status_updated.emit(f"❌ Erro ao processar {phone}: {str(e)}")
                        continue

                # Pausa entre lotes
                if i + self.batch_size < total_messages:
                    batch_pause = random.randint(15 * 60, 20 * 60)
                    self.status_updated.emit(f"⏳ Pausando {batch_pause//60} minutos entre lotes...")
                    time.sleep(batch_pause)

        except Exception as e:
            self.status_updated.emit(f"❌ Erro crítico: {str(e)}")
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
            self.status_updated.emit(f"📊 Processo finalizado. {messages_sent} mensagens enviadas de {total_messages}")
            self.finished_signal.emit(messages_sent)
    def send_invite_message(self, driver, phone, message):
        try:
            # Processar número e mensagem
            phone = ''.join(filter(str.isdigit, phone))
            if len(phone) == 11:
                phone = '55' + phone
            message = str(message).strip()

            # Criar URL e navegar
            url = f'https://web.whatsapp.com/send?phone={phone}'
            self.status_updated.emit("🔄 Abrindo conversa...")
            driver.get(url)

            # Aguardar carregamento inicial
            try:
                WebDriverWait(driver, 30).until(
                    lambda x: x.find_element(By.XPATH, "//*[@id='app']")
                )
            except Exception as e:
                raise Exception(f"WhatsApp não carregou: {str(e)}")

            # Verificar QR code
            try:
                qr_code = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//div[@data-ref]"))
                )
                if qr_code.is_displayed():
                    self.status_updated.emit("📱 Por favor, escaneie o QR code")
                    WebDriverWait(driver, 60).until_not(
                        EC.presence_of_element_located((By.XPATH, "//div[@data-ref]"))
                    )
            except:
                pass  # QR code não encontrado, continuar

            # Aguardar carregamento da conversa
            self.status_updated.emit("⏳ Aguardando conversa carregar...")
            main_loaded = False
            for _ in range(3):  # 3 tentativas
                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='main']"))
                    )
                    main_loaded = True
                    break
                except:
                    time.sleep(2)

            if not main_loaded:
                raise Exception("Conversa não carregou")

            # Aguardar o footer do chat carregar
            self.status_updated.emit("⌨️ Localizando campo de mensagem...")
            try:
                # Primeiro aguardar o footer aparecer
                footer = WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "#main footer"))
                )

                # Depois procurar o campo de mensagem dentro do footer
                input_box = WebDriverWait(footer, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'div[contenteditable="true"][role="textbox"]'))
                )
            except Exception as e:
                raise Exception(f"Campo de mensagem não encontrado no chat: {str(e)}")

            # Limpar, focar e digitar
            self.status_updated.emit("✍️ Digitando mensagem...")
            try:
                # Limpar o campo
                driver.execute_script("arguments[0].innerHTML = '';", input_box)
                input_box.click()  # Garantir foco
                time.sleep(1)

                # Digitar caractere por caractere
                for char in message:
                    input_box.send_keys(char)
                    time.sleep(0.1)
                time.sleep(1)

                # Verificar conteúdo
                msg_content = input_box.get_attribute('textContent').strip()
                if msg_content != message.strip():
                    raise Exception(f"Texto digitado incorretamente. Esperado: '{message}', Obtido: '{msg_content}'")
            except Exception as e:
                raise Exception(f"Erro ao digitar mensagem: {str(e)}")

            # Enviar mensagem
            self.status_updated.emit("📤 Enviando mensagem...")
            success = False

            # Tentar com botão primeiro
            try:
                send_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//span[@data-icon='send']"))
                )
                time.sleep(1)
                send_button.click()
                success = True
            except:
                # Tentar com Enter
                try:
                    input_box.send_keys(Keys.ENTER)
                    success = True
                except:
                    raise Exception("Não foi possível enviar a mensagem")

            if not success:
                raise Exception("Falha ao enviar mensagem")

            # Verificar envio
            self.status_updated.emit("🔍 Confirmando envio...")
            max_verify_attempts = 15  # Aumentado para 15 tentativas

            for attempt in range(max_verify_attempts):
                try:
                    # Procurar mensagens recentes
                    recent_messages = WebDriverWait(driver, 2).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, 'div.message-out'))
                    )

                    if recent_messages:
                        latest_message = recent_messages[-1]

                        # Verificar se a última mensagem contém nosso texto
                        message_text = latest_message.get_attribute('textContent') or ''
                        if message.strip() in message_text:
                            # Verificar indicadores de envio
                            message_status = latest_message.find_elements(By.CSS_SELECTOR,
                                'span[data-icon="msg-check"], span[data-icon="msg-dblcheck"], span[data-testid="msg-check"]')

                            if message_status:
                                self.status_updated.emit("✅ Mensagem enviada com sucesso!")
                                return True
                except Exception as e:
                    self.status_updated.emit(f"⏳ Verificando envio... ({attempt + 1}/{max_verify_attempts})")

                time.sleep(1)

            raise Exception("Não foi possível confirmar o envio após várias tentativas")

        except Exception as e:
            self.status_updated.emit(f"❌ Erro: {str(e)}")
            return False

    def send_invite_media(self, driver, phone, media_path):
        phone = ''.join(filter(str.isdigit, phone))
        if len(phone) == 11:
            phone = '55' + phone

        driver.get(f'https://web.whatsapp.com/send?phone={phone}')
        time.sleep(5)

        attach_button = driver.find_element(By.XPATH, '//div[@title="Anexar"]')
        attach_button.click()
        time.sleep(1)

        file_input = driver.find_element(By.XPATH, '//input[@type="file"]')
        file_input.send_keys(media_path)
        time.sleep(2)

        send_button = driver.find_element(By.XPATH, '//span[@data-icon="send"]')
        send_button.click()

class ConfigMensagensDialog(QDialog):
    def __init__(self, mensagens, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurar Mensagens")
        self.setMinimumWidth(500)

        layout = QVBoxLayout(self)

        # Campos de texto para cada tipo de mensagem
        self.campos = {}
        for tipo, desc in [
            ('A', 'Abordagem direta (50% dos contatos)'),
            ('B', 'Abertura com pergunta (25% dos contatos)'),
            ('C', 'Gancho de valor (25% dos contatos)')
        ]:
            grupo = QGroupBox(f"Mensagem Tipo {tipo}")
            grupo_layout = QVBoxLayout()

            desc_label = QLabel(desc)
            texto = QTextEdit()
            texto.setPlaceholderText("Digite a mensagem aqui...")
            texto.setText(mensagens.get(tipo, SendMessagesThread.MENSAGENS[tipo]))

            grupo_layout.addWidget(desc_label)
            grupo_layout.addWidget(texto)
            grupo.setLayout(grupo_layout)
            layout.addWidget(grupo)

            self.campos[tipo] = texto

        # Botões
        botoes = QHBoxLayout()
        salvar_btn = QPushButton("Salvar")
        cancelar_btn = QPushButton("Cancelar")

        salvar_btn.clicked.connect(self.accept)
        cancelar_btn.clicked.connect(self.reject)

        botoes.addWidget(salvar_btn)
        botoes.addWidget(cancelar_btn)
        layout.addLayout(botoes)

    def get_mensagens(self):
        return {tipo: campo.toPlainText() for tipo, campo in self.campos.items()}

class WhatsInviteMessage(QMainWindow):
    def __init__(self):
        super().__init__()
        self.df = None
        self.thread = None
        self.mensagens_config = {}  # Armazenar mensagens personalizadas
        self.initUI()
        self.apply_styles()

    def apply_styles(self):
        """Aplica estilos aos componentes da interface."""
        # Estilizar botões principais
        style_primary_button(self.send_button)
        style_primary_button(self.gmaps_search_btn)

        # Estilizar botões secundários
        style_secondary_button(self.download_btn)
        style_secondary_button(self.load_btn)
        style_secondary_button(self.clear_results_btn)
        style_secondary_button(self.export_selected_btn)

        # Estilizar botões de ação
        style_accent_button(self.send_to_whatsapp_btn)

        # Estilizar barras de progresso
        self.progress.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                background-color: #E0E0E0;
                text-align: center;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {PRIMARY_COLOR};
            }}
        """)

        self.gmaps_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                background-color: #E0E0E0;
                text-align: center;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {PRIMARY_COLOR};
            }}
        """)

        # Estilizar labels de status
        self.status_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 14px;")
        self.gmaps_status.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 14px;")

    def show_settings_dialog(self):
        """Exibe o diálogo de configurações."""
        dialog = SettingsDialog(self)
        if dialog.exec():
            # Recarregar configurações se necessário
            config = config_manager.load_config()

            # Atualizar a interface com as novas configurações
            # Por exemplo, atualizar diretórios padrão
            if hasattr(self, 'save_dir') and self.save_dir.text() == "":
                self.save_dir.setText(config.get("default_save_dir", ""))

            # Atualizar formato de exportação padrão
            if config.get("default_export_format") == "excel":
                if hasattr(self, 'excel_radio'):
                    self.excel_radio.setChecked(True)
            else:
                if hasattr(self, 'csv_radio'):
                    self.csv_radio.setChecked(True)

            # Atualizar modo headless
            if hasattr(self, 'headless_checkbox'):
                self.headless_checkbox.setChecked(config.get("headless_mode", True))

            # Exibir mensagem de confirmação
            self.status_label.setText("Configurações atualizadas com sucesso.")

    def show_help_dialog(self):
        """Exibe o diálogo de ajuda."""
        dialog = HelpDialog(self)
        dialog.exec()

    def initUI(self):
        self.setWindowTitle("PROSPECTO")
        # Reduzir tamanho mínimo para melhor responsividade
        self.setMinimumSize(800, 600)

        # Configurar tamanho inicial baseado na tela disponível
        screen = QApplication.primaryScreen().availableGeometry()
        if screen.width() >= 1200 and screen.height() >= 900:
            self.resize(1200, 900)
            self.showMaximized()
        else:
            # Para telas menores, usar 90% da tela disponível
            width = int(screen.width() * 0.9)
            height = int(screen.height() * 0.9)
            self.resize(width, height)
            self.move(int(screen.width() * 0.05), int(screen.height() * 0.05))

        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(15)
        # Margens responsivas - menores em telas pequenas
        screen = QApplication.primaryScreen().availableGeometry()
        if screen.width() < 1000:
            layout.setContentsMargins(15, 15, 15, 15)
        else:
            layout.setContentsMargins(30, 30, 30, 30)

        # Criar stack widget para navegação entre telas
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)

        # Criar tela inicial
        self.create_home_screen()

        # Criar telas das funcionalidades
        self.create_automated_search_screen()
        self.create_gmaps_screen()
        self.create_whatsapp_screen()

        # Adicionar rodapé
        footer_widget = QWidget()
        footer_widget.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px;")
        footer_layout = QHBoxLayout(footer_widget)

        # Copyright
        copyright_label = QLabel("© 2025 PROSPECTO - Todos os direitos reservados")
        copyright_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 12px;")
        footer_layout.addWidget(copyright_label)

        # Botões de ajuda, configurações e sair
        help_button = QPushButton("Ajuda")
        help_button.setIcon(QIcon.fromTheme("help-about"))
        style_secondary_button(help_button)
        help_button.clicked.connect(self.show_help_dialog)

        settings_button = QPushButton("Configurações")
        settings_button.setIcon(QIcon.fromTheme("preferences-system"))
        style_secondary_button(settings_button)
        settings_button.clicked.connect(self.show_settings_dialog)

        exit_button = QPushButton("Sair")
        exit_button.setIcon(QIcon.fromTheme("application-exit"))
        style_accent_button(exit_button)
        exit_button.clicked.connect(self.close)

        footer_layout.addStretch()
        footer_layout.addWidget(help_button)
        footer_layout.addWidget(settings_button)
        footer_layout.addWidget(exit_button)

        layout.addWidget(footer_widget)

    def create_home_screen(self):
        """Cria a tela inicial com botões para as funcionalidades."""
        home_widget = QWidget()
        layout = QVBoxLayout(home_widget)
        layout.setSpacing(30)
        layout.setContentsMargins(50, 50, 50, 50)

        # Logo principal
        logo_label = QLabel()
        logo_pixmap = QPixmap("assets/img/logo.png")
        if not logo_pixmap.isNull():
            # Redimensionar a logo para um tamanho apropriado (responsivo)
            screen = QApplication.primaryScreen().availableGeometry()
            if screen.width() < 1000:
                logo_size = 120  # Menor para telas pequenas
            else:
                logo_size = 180  # Maior para telas grandes

            scaled_pixmap = logo_pixmap.scaled(
                logo_size, logo_size,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            logo_label.setPixmap(scaled_pixmap)
        else:
            # Fallback se a logo não for encontrada
            logo_label.setText("PROSPECTO")
            logo_label.setStyleSheet(f"""
                color: {PRIMARY_COLOR};
                font-size: 48px;
                font-weight: 700;
                font-family: {FONT_FAMILY};
                letter-spacing: 2px;
                text-align: center;
            """)

        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)

        # Subtítulo
        subtitle = QLabel("Sistema de Geração de Leads")
        subtitle.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 20px;
            font-family: {FONT_FAMILY};
            font-weight: 400;
            text-align: center;
            margin-bottom: 30px;
        """)
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle)

        # Container para os botões
        buttons_container = QWidget()
        buttons_layout = QVBoxLayout(buttons_container)
        buttons_layout.setSpacing(20)

        # Botão Busca Automatizada
        automated_btn = QPushButton("BUSCA AUTOMATIZADA")
        automated_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 {PRIMARY_COLOR}, stop: 1 {SECONDARY_COLOR});
                color: white;
                border: none;
                border-radius: 16px;
                padding: 24px 48px;
                font-weight: 700;
                font-size: 18px;
                font-family: {FONT_FAMILY};
                min-height: 80px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 #0056CC, stop: 1 #2BC5A8);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 #003D99, stop: 1 #1FA085);
            }}
        """)
        automated_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(1))
        buttons_layout.addWidget(automated_btn)

        # Botão Google Maps
        gmaps_btn = QPushButton("GOOGLE MAPS - BUSCA UNITÁRIA")
        gmaps_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 {ACCENT_COLOR}, stop: 1 #FF9500);
                color: white;
                border: none;
                border-radius: 16px;
                padding: 24px 48px;
                font-weight: 700;
                font-size: 18px;
                font-family: {FONT_FAMILY};
                min-height: 80px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 #E6A500, stop: 1 #E6850E);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 #CC9400, stop: 1 #CC7A0D);
            }}
        """)
        gmaps_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(2))
        buttons_layout.addWidget(gmaps_btn)

        # Botão Disparador
        whatsapp_btn = QPushButton("DISPARADOR")
        whatsapp_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 {SECONDARY_COLOR}, stop: 1 #25B7A3);
                color: white;
                border: none;
                border-radius: 16px;
                padding: 24px 48px;
                font-weight: 700;
                font-size: 18px;
                font-family: {FONT_FAMILY};
                min-height: 80px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 #2BC5A8, stop: 1 #1FA085);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                          stop: 0 #1FA085, stop: 1 #198A70);
            }}
        """)
        whatsapp_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(3))
        buttons_layout.addWidget(whatsapp_btn)

        layout.addWidget(buttons_container)
        layout.addStretch()

        self.stacked_widget.addWidget(home_widget)

    def create_automated_search_screen(self):
        """Cria a tela de busca automatizada."""
        # Criar a aba de busca automatizada
        automated_search_tab = AutomatedSearchTab(self)

        # Criar widget container com botão voltar
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(20, 20, 20, 20)

        # Botão voltar
        back_btn = QPushButton("← Voltar")
        back_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {CARD_COLOR};
                color: {TEXT_PRIMARY};
                border: 2px solid {PRIMARY_COLOR};
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 14px;
                max-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {PRIMARY_COLOR};
                color: white;
            }}
        """)
        back_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(0))
        layout.addWidget(back_btn)

        # Adicionar a aba
        layout.addWidget(automated_search_tab)

        self.stacked_widget.addWidget(container)

    def create_gmaps_screen(self):
        """Cria a tela do Google Maps."""
        # Criar widget container com botão voltar
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(20, 20, 20, 20)

        # Botão voltar
        back_btn = QPushButton("← Voltar")
        back_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {CARD_COLOR};
                color: {TEXT_PRIMARY};
                border: 2px solid {PRIMARY_COLOR};
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 14px;
                max-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {PRIMARY_COLOR};
                color: white;
            }}
        """)
        back_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(0))
        layout.addWidget(back_btn)

        # Aba do Google Maps
        gmaps_tab = QWidget()
        gmaps_layout = QVBoxLayout(gmaps_tab)
        gmaps_layout.setSpacing(20)

        # Criar um splitter para dividir a tela em duas partes
        splitter = QSplitter(Qt.Orientation.Vertical)
        gmaps_layout.addWidget(splitter)

        # Parte superior - Formulário de busca
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)

        # Campos para a busca no Google Maps
        gmaps_form_frame = QFrame()
        gmaps_form_frame.setObjectName("settingsFrame")
        gmaps_form_layout = QVBoxLayout(gmaps_form_frame)

        # Título da seção
        title_label = QLabel("Configurações de Busca")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        gmaps_form_layout.addWidget(title_label)

        # Layout em grid para os campos de entrada
        form_grid = QHBoxLayout()

        # Coluna da esquerda
        left_column = QVBoxLayout()

        # Campo para o termo de busca
        search_layout = QHBoxLayout()
        search_label = QLabel("Termo de Busca:")
        self.search_term = QLineEdit()
        self.search_term.setPlaceholderText("Ex.: Restaurante")
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_term)
        left_column.addLayout(search_layout)

        # Campo para a localização
        location_layout = QHBoxLayout()
        location_label = QLabel("Localização:")
        self.location = QLineEdit()
        self.location.setPlaceholderText("Ex.: Rio de Janeiro")
        location_layout.addWidget(location_label)
        location_layout.addWidget(self.location)
        left_column.addLayout(location_layout)

        # Coluna da direita
        right_column = QVBoxLayout()

        # Campo para o número de resultados
        results_layout = QHBoxLayout()
        results_label = QLabel("Número de Resultados:")
        self.total_results = QSpinBox()
        self.total_results.setMinimum(1)
        self.total_results.setMaximum(500)
        self.total_results.setValue(50)
        results_layout.addWidget(results_label)
        results_layout.addWidget(self.total_results)
        right_column.addLayout(results_layout)

        # Opções de formato de arquivo
        format_layout = QHBoxLayout()
        format_label = QLabel("Formato do Arquivo:")
        self.format_group = QButtonGroup()
        self.excel_radio = QRadioButton("Excel")
        self.csv_radio = QRadioButton("CSV")

        # Usar a configuração padrão
        config = config_manager.load_config()
        if config.get("default_export_format", "excel") == "excel":
            self.excel_radio.setChecked(True)
        else:
            self.csv_radio.setChecked(True)

        self.format_group.addButton(self.excel_radio)
        self.format_group.addButton(self.csv_radio)
        format_layout.addWidget(format_label)
        format_layout.addWidget(self.excel_radio)
        format_layout.addWidget(self.csv_radio)
        right_column.addLayout(format_layout)

        # Opção de modo headless
        headless_layout = QHBoxLayout()
        headless_label = QLabel("Modo de Execução:")
        self.headless_checkbox = QCheckBox("Executar em segundo plano (headless)")

        # Usar a configuração padrão
        config = config_manager.load_config()
        self.headless_checkbox.setChecked(config.get("headless_mode", True))

        self.headless_checkbox.setToolTip("Quando ativado, o navegador funciona em segundo plano sem exibir janela")
        headless_layout.addWidget(headless_label)
        headless_layout.addWidget(self.headless_checkbox)
        right_column.addLayout(headless_layout)

        # Adicionar as colunas ao grid
        form_grid.addLayout(left_column)
        form_grid.addLayout(right_column)
        gmaps_form_layout.addLayout(form_grid)

        # Campo para o diretório de salvamento
        save_dir_layout = QHBoxLayout()
        save_dir_label = QLabel("Diretório para Salvar:")
        self.save_dir = QLineEdit()
        self.save_dir.setReadOnly(True)
        self.select_dir_btn = QPushButton("Selecionar")
        self.select_dir_btn.setMaximumWidth(80)
        style_secondary_button(self.select_dir_btn)
        self.select_dir_btn.clicked.connect(self.select_save_directory)
        save_dir_layout.addWidget(save_dir_label)
        save_dir_layout.addWidget(self.save_dir)
        save_dir_layout.addWidget(self.select_dir_btn)
        gmaps_form_layout.addLayout(save_dir_layout)

        # Adicionar opções avançadas
        advanced_layout = QHBoxLayout()

        # Filtro de categoria
        category_layout = QHBoxLayout()
        category_label = QLabel("Filtrar por Categoria:")
        self.category_filter = QComboBox()
        self.category_filter.addItems(["Todas", "Restaurantes", "Lojas", "Serviços", "Hotéis", "Outros"])
        self.category_filter.currentIndexChanged.connect(self.apply_filters)
        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_filter)
        advanced_layout.addLayout(category_layout)

        # Filtro de avaliação mínima
        rating_layout = QHBoxLayout()
        rating_label = QLabel("Avaliação Mínima:")
        self.rating_filter = QComboBox()
        self.rating_filter.addItems(["Qualquer", "3+", "4+", "4.5+"])
        self.rating_filter.currentIndexChanged.connect(self.apply_filters)
        rating_layout.addWidget(rating_label)
        rating_layout.addWidget(self.rating_filter)
        advanced_layout.addLayout(rating_layout)

        gmaps_form_layout.addLayout(advanced_layout)

        # Botões de ação
        buttons_layout = QHBoxLayout()

        # Botão para iniciar a busca
        self.gmaps_search_btn = QPushButton("Iniciar Busca no Google Maps")
        self.gmaps_search_btn.setObjectName("sendButton")
        style_primary_button(self.gmaps_search_btn)
        self.gmaps_search_btn.clicked.connect(self.start_gmaps_search)

        # Botão para limpar os resultados
        self.clear_results_btn = QPushButton("Limpar Resultados")
        style_accent_button(self.clear_results_btn)
        self.clear_results_btn.clicked.connect(self.clear_gmaps_results)

        buttons_layout.addWidget(self.gmaps_search_btn)
        buttons_layout.addWidget(self.clear_results_btn)
        gmaps_form_layout.addLayout(buttons_layout)

        top_layout.addWidget(gmaps_form_frame)

        # Barra de progresso para o Google Maps
        self.gmaps_progress = QProgressBar()
        top_layout.addWidget(self.gmaps_progress)

        # Status label para o Google Maps
        self.gmaps_status = QLabel()
        self.gmaps_status.setObjectName("statusLabel")
        top_layout.addWidget(self.gmaps_status)

        # Parte inferior - Tabela de resultados
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        bottom_layout.setContentsMargins(0, 0, 0, 0)

        # Título da seção de resultados
        results_title = QLabel("Resultados da Busca")
        results_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        bottom_layout.addWidget(results_title)

        # Tabela para exibir os resultados
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            "Nome", "Endereço", "Telefone", "Website", "Avaliação", "Categoria", "Ações"
        ])

        # Configurar a tabela de forma responsiva
        screen = QApplication.primaryScreen().availableGeometry()
        if screen.width() < 1000:
            # Para telas menores, usar configuração mais compacta
            self.results_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Nome
            self.results_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Endereço
            self.results_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Telefone
            self.results_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Website
            self.results_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Avaliação
            self.results_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Categoria
            self.results_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Ações
            # Definir larguras mínimas para colunas importantes
            self.results_table.setColumnWidth(0, 150)  # Nome
            self.results_table.setColumnWidth(1, 120)  # Endereço
            self.results_table.setColumnWidth(2, 100)  # Telefone
        else:
            # Para telas maiores, usar configuração padrão
            self.results_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Nome
            self.results_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Endereço
            self.results_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Telefone
            self.results_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Website
            self.results_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Avaliação
            self.results_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Categoria
            self.results_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Ações

        bottom_layout.addWidget(self.results_table)

        # Botões de ação para os resultados
        results_buttons_layout = QHBoxLayout()

        # Botão para exportar resultados selecionados
        self.export_selected_btn = QPushButton("Exportar Selecionados")
        style_secondary_button(self.export_selected_btn)
        self.export_selected_btn.clicked.connect(self.export_selected_results)

        # Botão para enviar para WhatsApp
        self.send_to_whatsapp_btn = QPushButton("Enviar para WhatsApp")
        style_secondary_button(self.send_to_whatsapp_btn)
        self.send_to_whatsapp_btn.clicked.connect(self.send_results_to_whatsapp)

        results_buttons_layout.addWidget(self.export_selected_btn)
        results_buttons_layout.addWidget(self.send_to_whatsapp_btn)
        results_buttons_layout.addStretch()

        bottom_layout.addLayout(results_buttons_layout)

        # Adicionar widgets ao splitter
        splitter.addWidget(top_widget)
        splitter.addWidget(bottom_widget)

        # Definir tamanhos iniciais do splitter
        splitter.setSizes([300, 500])

        # Adicionar a aba ao container
        layout.addWidget(gmaps_tab)

        self.stacked_widget.addWidget(container)

    def create_whatsapp_screen(self):
        """Cria a tela do WhatsApp (Disparador)."""
        # Criar widget container com botão voltar
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(20, 20, 20, 20)

        # Botão voltar
        back_btn = QPushButton("← Voltar")
        back_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {CARD_COLOR};
                color: {TEXT_PRIMARY};
                border: 2px solid {PRIMARY_COLOR};
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 14px;
                max-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {PRIMARY_COLOR};
                color: white;
            }}
        """)
        back_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(0))
        layout.addWidget(back_btn)

        # Aba de WhatsApp
        whatsapp_tab = QWidget()
        whatsapp_layout = QVBoxLayout(whatsapp_tab)
        whatsapp_layout.setSpacing(10)

        # Criar área de scroll para toda a aba
        whatsapp_scroll = QScrollArea()
        whatsapp_scroll.setWidgetResizable(True)
        whatsapp_scroll.setFrameShape(QFrame.Shape.NoFrame)
        whatsapp_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        whatsapp_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Widget principal dentro do scroll
        whatsapp_main_widget = QWidget()
        whatsapp_main_layout = QVBoxLayout(whatsapp_main_widget)
        whatsapp_main_layout.setSpacing(15)
        whatsapp_main_layout.setContentsMargins(10, 10, 10, 10)

        # Criar um splitter para dividir a tela em duas partes
        whatsapp_splitter = QSplitter(Qt.Orientation.Vertical)
        whatsapp_main_layout.addWidget(whatsapp_splitter)

        # Parte superior - Configurações e controles
        top_whatsapp_widget = QWidget()
        top_whatsapp_layout = QVBoxLayout(top_whatsapp_widget)
        top_whatsapp_layout.setContentsMargins(5, 5, 5, 5)

        # Configurações do WhatsApp
        settings_frame = QFrame()
        settings_frame.setObjectName("settingsFrame")
        settings_layout = QVBoxLayout(settings_frame)
        settings_layout.setSpacing(10)

        # Título da seção
        whatsapp_title = QLabel("Configurações de Mensagens")
        whatsapp_title.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 5px;")
        settings_layout.addWidget(whatsapp_title)

        # Layout responsivo para as configurações
        screen = QApplication.primaryScreen().availableGeometry()
        if screen.width() < 1000:
            # Para telas menores, usar layout vertical
            config_layout = QVBoxLayout()

            # Intervalos
            interval_text = "Intervalos: 8-12 min entre mensagens, lotes de 5-7 contatos"
            interval_label = QLabel(interval_text)
            interval_label.setStyleSheet("color: #576574; font-size: 11px; padding: 5px;")
            interval_label.setWordWrap(True)
            config_layout.addWidget(interval_label)

            # Botão para configurar mensagens
            config_btn = QPushButton("Configurar Mensagens")
            config_btn.setMaximumHeight(35)
            style_secondary_button(config_btn)
            config_btn.clicked.connect(self.configurar_mensagens)
            config_layout.addWidget(config_btn)
        else:
            # Para telas maiores, usar layout horizontal
            config_layout = QHBoxLayout()

            # Coluna da esquerda - Intervalos
            interval_text = (
                "Intervalos implementados:\n"
                "• Entre mensagens: 8-12 minutos (±2 min)\n"
                "• Entre lotes: 15-20 minutos\n"
                "• Tamanho do lote: 5-7 contatos"
            )
            interval_label = QLabel(interval_text)
            interval_label.setStyleSheet("color: #576574; padding: 10px;")
            config_layout.addWidget(interval_label)

            # Coluna da direita - Opções de personalização
            config_btn = QPushButton("Configurar Mensagens")
            style_secondary_button(config_btn)
            config_btn.clicked.connect(self.configurar_mensagens)
            config_layout.addWidget(config_btn)

        settings_layout.addLayout(config_layout)

        # Botões para carregar/salvar dados - layout responsivo
        if screen.width() < 1000:
            # Para telas menores, usar layout vertical com botões menores
            buttons_layout = QVBoxLayout()
            button_height = 30

            self.download_btn = QPushButton("Download Template")
            self.download_btn.setMaximumHeight(button_height)
            style_secondary_button(self.download_btn)

            self.load_btn = QPushButton("Carregar Arquivo")
            self.load_btn.setMaximumHeight(button_height)
            style_secondary_button(self.load_btn)

            self.export_contacts_btn = QPushButton("Exportar Contatos")
            self.export_contacts_btn.setMaximumHeight(button_height)
            style_secondary_button(self.export_contacts_btn)
            self.export_contacts_btn.clicked.connect(self.export_whatsapp_contacts)

            buttons_layout.addWidget(self.download_btn)
            buttons_layout.addWidget(self.load_btn)
            buttons_layout.addWidget(self.export_contacts_btn)
        else:
            # Para telas maiores, usar layout horizontal
            buttons_layout = QHBoxLayout()

            self.download_btn = QPushButton("Download Template")
            style_secondary_button(self.download_btn)
            self.load_btn = QPushButton("Carregar Arquivo")
            style_secondary_button(self.load_btn)
            self.export_contacts_btn = QPushButton("Exportar Contatos")
            style_secondary_button(self.export_contacts_btn)
            self.export_contacts_btn.clicked.connect(self.export_whatsapp_contacts)

            buttons_layout.addWidget(self.download_btn)
            buttons_layout.addWidget(self.load_btn)
            buttons_layout.addWidget(self.export_contacts_btn)
            buttons_layout.addStretch()

        settings_layout.addLayout(buttons_layout)

        # Opções de filtro para mensagens - layout responsivo
        if screen.width() < 1000:
            # Para telas menores, usar layout vertical compacto
            filter_layout = QVBoxLayout()

            # Primeira linha - Tipo e Status
            filter_row1 = QHBoxLayout()

            msg_type_label = QLabel("Tipo:")
            msg_type_label.setMaximumWidth(40)
            self.msg_type_filter = QComboBox()
            self.msg_type_filter.addItems(["Todos", "Tipo A", "Tipo B", "Tipo C"])
            self.msg_type_filter.currentIndexChanged.connect(self.filter_messages)

            status_label = QLabel("Status:")
            status_label.setMaximumWidth(45)
            self.status_filter = QComboBox()
            self.status_filter.addItems(["Todos", "Pendentes", "Enviados"])
            self.status_filter.currentIndexChanged.connect(self.filter_messages)

            filter_row1.addWidget(msg_type_label)
            filter_row1.addWidget(self.msg_type_filter)
            filter_row1.addWidget(status_label)
            filter_row1.addWidget(self.status_filter)

            # Segunda linha - Botão limpar
            self.clear_filters_btn = QPushButton("Limpar Filtros")
            self.clear_filters_btn.setMaximumHeight(30)
            style_accent_button(self.clear_filters_btn)
            self.clear_filters_btn.clicked.connect(self.clear_message_filters)

            filter_layout.addLayout(filter_row1)
            filter_layout.addWidget(self.clear_filters_btn)
        else:
            # Para telas maiores, usar layout horizontal
            filter_layout = QHBoxLayout()

            # Filtro por tipo de mensagem
            msg_type_layout = QHBoxLayout()
            msg_type_label = QLabel("Filtrar por Tipo:")
            self.msg_type_filter = QComboBox()
            self.msg_type_filter.addItems(["Todos", "Tipo A", "Tipo B", "Tipo C"])
            self.msg_type_filter.currentIndexChanged.connect(self.filter_messages)
            msg_type_layout.addWidget(msg_type_label)
            msg_type_layout.addWidget(self.msg_type_filter)
            filter_layout.addLayout(msg_type_layout)

            # Filtro por status de envio
            status_layout = QHBoxLayout()
            status_label = QLabel("Status:")
            self.status_filter = QComboBox()
            self.status_filter.addItems(["Todos", "Pendentes", "Enviados"])
            self.status_filter.currentIndexChanged.connect(self.filter_messages)
            status_layout.addWidget(status_label)
            status_layout.addWidget(self.status_filter)
            filter_layout.addLayout(status_layout)

            # Botão para limpar filtros
            self.clear_filters_btn = QPushButton("Limpar Filtros")
            style_accent_button(self.clear_filters_btn)
            self.clear_filters_btn.clicked.connect(self.clear_message_filters)
            filter_layout.addWidget(self.clear_filters_btn)
            filter_layout.addStretch()

        settings_layout.addLayout(filter_layout)

        top_whatsapp_layout.addWidget(settings_frame)

        # Barra de progresso para o WhatsApp
        self.progress = QProgressBar()
        top_whatsapp_layout.addWidget(self.progress)

        # Status label para o WhatsApp
        self.status_label = QLabel()
        self.status_label.setObjectName("statusLabel")
        top_whatsapp_layout.addWidget(self.status_label)

        # Parte inferior - Lista de mensagens
        bottom_whatsapp_widget = QWidget()
        bottom_whatsapp_layout = QVBoxLayout(bottom_whatsapp_widget)
        bottom_whatsapp_layout.setContentsMargins(0, 0, 0, 0)

        # Título da seção de mensagens
        messages_title = QLabel("Lista de Mensagens")
        messages_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        bottom_whatsapp_layout.addWidget(messages_title)

        # TreeWidget para exibir as mensagens
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(['Telefone', 'Mensagem', 'Horário', 'Status'])

        # Configurar colunas de forma responsiva
        screen = QApplication.primaryScreen().availableGeometry()
        if screen.width() < 1000:
            # Para telas menores, usar larguras mais compactas
            self.tree.setColumnWidth(0, 120)  # Telefone
            self.tree.setColumnWidth(1, 250)  # Mensagem
            self.tree.setColumnWidth(2, 100)  # Horário
            self.tree.setColumnWidth(3, 80)   # Status
        else:
            # Para telas maiores, usar larguras padrão
            self.tree.setColumnWidth(0, 150)  # Telefone
            self.tree.setColumnWidth(1, 400)  # Mensagem
            self.tree.setColumnWidth(2, 150)  # Horário
            self.tree.setColumnWidth(3, 100)  # Status
        bottom_whatsapp_layout.addWidget(self.tree)

        # Botões de ação para as mensagens - layout responsivo
        screen = QApplication.primaryScreen().availableGeometry()
        if screen.width() < 1000:
            # Para telas menores, usar layout vertical com botões menores
            message_buttons_layout = QVBoxLayout()
            button_height = 35

            self.send_button = QPushButton("Enviar Mensagens")
            self.send_button.setObjectName("sendButton")
            self.send_button.setMaximumHeight(button_height)
            style_primary_button(self.send_button)
            self.send_button.clicked.connect(self.start_sending)

            self.send_selected_btn = QPushButton("Enviar Selecionados")
            self.send_selected_btn.setMaximumHeight(button_height)
            style_secondary_button(self.send_selected_btn)
            self.send_selected_btn.clicked.connect(self.send_selected_messages)

            self.clear_messages_btn = QPushButton("Limpar Lista")
            self.clear_messages_btn.setMaximumHeight(button_height)
            style_accent_button(self.clear_messages_btn)
            self.clear_messages_btn.clicked.connect(self.clear_messages)

            message_buttons_layout.addWidget(self.send_button)
            message_buttons_layout.addWidget(self.send_selected_btn)
            message_buttons_layout.addWidget(self.clear_messages_btn)
        else:
            # Para telas maiores, usar layout horizontal
            message_buttons_layout = QHBoxLayout()

            self.send_button = QPushButton("Enviar Mensagens")
            self.send_button.setObjectName("sendButton")
            style_primary_button(self.send_button)
            self.send_button.clicked.connect(self.start_sending)

            self.send_selected_btn = QPushButton("Enviar Selecionados")
            style_secondary_button(self.send_selected_btn)
            self.send_selected_btn.clicked.connect(self.send_selected_messages)

            self.clear_messages_btn = QPushButton("Limpar Lista")
            style_accent_button(self.clear_messages_btn)
            self.clear_messages_btn.clicked.connect(self.clear_messages)

            message_buttons_layout.addWidget(self.send_button)
            message_buttons_layout.addWidget(self.send_selected_btn)
            message_buttons_layout.addWidget(self.clear_messages_btn)
            message_buttons_layout.addStretch()

        bottom_whatsapp_layout.addLayout(message_buttons_layout)

        # Adicionar widgets ao splitter
        whatsapp_splitter.addWidget(top_whatsapp_widget)
        whatsapp_splitter.addWidget(bottom_whatsapp_widget)

        # Definir tamanhos iniciais do splitter
        whatsapp_splitter.setSizes([300, 500])

        # Adicionar o scroll area à aba
        whatsapp_scroll.setWidget(whatsapp_main_widget)
        whatsapp_layout.addWidget(whatsapp_scroll)

        # Adicionar a aba ao container
        layout.addWidget(whatsapp_tab)

        self.stacked_widget.addWidget(container)

        # Conectar os botões da aba WhatsApp
        self.download_btn.clicked.connect(self.download_template)
        self.load_btn.clicked.connect(self.load_file)
        self.send_button.clicked.connect(self.start_sending)

    def resizeEvent(self, event):
        """Evento chamado quando a janela é redimensionada."""
        super().resizeEvent(event)

        # Ajustar layout baseado no novo tamanho
        if hasattr(self, 'tree') and hasattr(self, 'results_table'):
            current_width = self.width()

            if current_width < 1000:
                # Ajustar colunas da TreeWidget para telas menores
                self.tree.setColumnWidth(0, 120)  # Telefone
                self.tree.setColumnWidth(1, 250)  # Mensagem
                self.tree.setColumnWidth(2, 100)  # Horário
                self.tree.setColumnWidth(3, 80)   # Status

                # Ajustar colunas da tabela de resultados para telas menores
                self.results_table.setColumnWidth(0, 150)  # Nome
                self.results_table.setColumnWidth(1, 120)  # Endereço
                self.results_table.setColumnWidth(2, 100)  # Telefone
            else:
                # Ajustar colunas para telas maiores
                self.tree.setColumnWidth(0, 150)  # Telefone
                self.tree.setColumnWidth(1, 400)  # Mensagem
                self.tree.setColumnWidth(2, 150)  # Horário
                self.tree.setColumnWidth(3, 100)  # Status

    def select_save_directory(self):
        """Seleciona o diretório para salvar os resultados da busca no Google Maps."""
        # Usar o diretório padrão das configurações como ponto de partida
        config = config_manager.load_config()
        default_dir = config.get("default_save_dir", "")

        directory = QFileDialog.getExistingDirectory(
            self,
            "Selecionar Diretório",
            default_dir
        )

        if directory:
            self.save_dir.setText(directory)

    def start_gmaps_search(self):
        """Inicia a busca no Google Maps."""
        # Validar campos
        if not self.search_term.text():
            QMessageBox.warning(self, "Erro", "Por favor, informe um termo de busca.")
            return

        if not self.location.text():
            QMessageBox.warning(self, "Erro", "Por favor, informe uma localização.")
            return

        if not self.save_dir.text():
            QMessageBox.warning(self, "Erro", "Por favor, selecione um diretório para salvar os resultados.")
            return

        # Obter valores dos campos
        search_for = f"{self.search_term.text()} {self.location.text()}"
        total = self.total_results.value()
        location = self.location.text()
        save_dir = self.save_dir.text()
        file_format = "excel" if self.excel_radio.isChecked() else "csv"
        headless_mode = self.headless_checkbox.isChecked()

        # Desabilitar botão de busca
        self.gmaps_search_btn.setEnabled(False)
        self.gmaps_progress.setValue(0)

        # Criar e iniciar thread
        self.gmaps_thread = GoogleMapsScraperThread(
            search_for=search_for,
            total=total,
            location=location,
            save_dir=save_dir,
            file_format=file_format,
            headless_mode=headless_mode
        )
        self.gmaps_thread.progress_updated.connect(self.gmaps_progress.setValue)
        self.gmaps_thread.status_updated.connect(self.gmaps_status.setText)
        self.gmaps_thread.finished_signal.connect(self.gmaps_search_finished)
        self.gmaps_thread.continue_query.connect(self.show_continue_dialog)
        self.gmaps_thread.start()

    def gmaps_search_finished(self, result):
        """Callback chamado quando a busca no Google Maps é finalizada."""
        self.gmaps_search_btn.setEnabled(True)

        if "ERRO" not in result:
            # Extrair o caminho do arquivo do resultado
            try:
                file_path = result.split("Arquivo salvo em: ")[1].strip()

                # Carregar o arquivo para exibir na tabela
                try:
                    df = pd.read_excel(file_path) if file_path.endswith('.xlsx') else pd.read_csv(file_path)

                    # Limpar a tabela
                    self.results_table.setRowCount(0)

                    # Preencher a tabela com os resultados
                    for i, row in df.iterrows():
                        row_position = self.results_table.rowCount()
                        self.results_table.insertRow(row_position)

                        # Adicionar os dados às células
                        self.results_table.setItem(row_position, 0, QTableWidgetItem(str(row.get('name', ''))))
                        self.results_table.setItem(row_position, 1, QTableWidgetItem(str(row.get('address', ''))))
                        self.results_table.setItem(row_position, 2, QTableWidgetItem(str(row.get('phone_number', ''))))
                        self.results_table.setItem(row_position, 3, QTableWidgetItem(str(row.get('website', ''))))
                        self.results_table.setItem(row_position, 4, QTableWidgetItem(str(row.get('rating', ''))))
                        self.results_table.setItem(row_position, 5, QTableWidgetItem(str(row.get('category', ''))))

                        # Adicionar botão de ação na última coluna
                        action_btn = QPushButton("📱")
                        action_btn.setToolTip("Enviar para WhatsApp")
                        action_btn.clicked.connect(lambda _, row=row_position: self.send_single_to_whatsapp(row))
                        self.results_table.setCellWidget(row_position, 6, action_btn)

                    # Aplicar filtros se necessário
                    self.apply_filters()

                    # Mostrar mensagem de sucesso
                    QMessageBox.information(
                        self,
                        "Sucesso",
                        f"Busca finalizada com sucesso!\n\n"
                        f"• {self.results_table.rowCount()} resultados encontrados\n"
                        f"• Arquivo salvo em: {file_path}\n\n"
                        "Você pode selecionar resultados na tabela para exportar ou enviar para o WhatsApp."
                    )

                except Exception as e:
                    QMessageBox.warning(
                        self,
                        "Aviso",
                        f"Arquivo salvo com sucesso, mas não foi possível carregar os resultados na tabela: {str(e)}\n\n{result}"
                    )
            except:
                QMessageBox.information(self, "Sucesso", f"Busca finalizada com sucesso!\n\n{result}")
        else:
            QMessageBox.warning(self, "Erro", result)

    def send_single_to_whatsapp(self, row):
        """Envia um único resultado para o WhatsApp."""
        phone = self.results_table.item(row, 2).text()
        # Limpar o número de telefone (remover caracteres não numéricos)
        phone = ''.join(filter(str.isdigit, phone))

        if not phone:
            QMessageBox.warning(self, "Aviso", "Este resultado não possui um número de telefone válido.")
            return

        # Criar DataFrame com o dado selecionado
        data = [{
            'telefone': phone,
            'nome': self.results_table.item(row, 0).text(),
            'endereco': self.results_table.item(row, 1).text(),
            'website': self.results_table.item(row, 3).text(),
            'categoria': self.results_table.item(row, 5).text()
        }]

        # Criar DataFrame
        self.df = pd.DataFrame(data)

        # Distribuir os tipos de mensagem
        self.distribuir_mensagens()

        # Atualizar a visualização
        self.update_preview()

        # Mudar para a tela do WhatsApp
        self.stacked_widget.setCurrentIndex(3)

        QMessageBox.information(
            self,
            "Sucesso",
            "Contato carregado para envio de mensagem pelo WhatsApp.\n\n"
            "Você pode agora configurar a mensagem e iniciar o envio."
        )

    def apply_filters(self):
        """Aplica os filtros selecionados à tabela de resultados."""
        category = self.category_filter.currentText()
        rating = self.rating_filter.currentText()

        # Percorrer todas as linhas da tabela
        for row in range(self.results_table.rowCount()):
            show_row = True

            # Filtrar por categoria
            if category != "Todas":
                row_category = self.results_table.item(row, 5).text()
                if category.lower() not in row_category.lower():
                    show_row = False

            # Filtrar por avaliação
            if rating != "Qualquer":
                row_rating = self.results_table.item(row, 4).text()
                try:
                    min_rating = float(rating.replace("+", ""))
                    if row_rating and float(row_rating.split()[0]) < min_rating:
                        show_row = False
                except (ValueError, IndexError):
                    pass  # Ignorar erros de conversão

            # Mostrar ou ocultar a linha
            self.results_table.setRowHidden(row, not show_row)

    def filter_messages(self):
        """Filtra as mensagens na lista de acordo com os critérios selecionados."""
        msg_type = self.msg_type_filter.currentText()
        status = self.status_filter.currentText()

        # Percorrer todas as linhas da árvore
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            show_item = True

            # Filtrar por tipo de mensagem
            if msg_type != "Todos":
                message_text = item.text(1)
                if not message_text.startswith(msg_type):
                    show_item = False

            # Filtrar por status
            if status != "Todos":
                item_status = item.text(3)
                if status == "Pendentes" and item_status != "Pendente":
                    show_item = False
                elif status == "Enviados" and item_status == "Pendente":
                    show_item = False

            # Mostrar ou ocultar o item
            item.setHidden(not show_item)

    def clear_message_filters(self):
        """Limpa os filtros de mensagens."""
        self.msg_type_filter.setCurrentIndex(0)  # "Todos"
        self.status_filter.setCurrentIndex(0)    # "Todos"

        # Mostrar todos os itens
        for i in range(self.tree.topLevelItemCount()):
            self.tree.topLevelItem(i).setHidden(False)

    def clear_messages(self):
        """Limpa a lista de mensagens."""
        reply = QMessageBox.question(
            self,
            "Confirmar Limpeza",
            "Tem certeza que deseja limpar a lista de mensagens?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.tree.clear()
            self.df = None
            self.status_label.setText("Lista de mensagens limpa.")

    def send_selected_messages(self):
        """Envia apenas as mensagens selecionadas."""
        # Verificar se há itens selecionados
        selected_items = self.tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Aviso", "Por favor, selecione pelo menos uma mensagem para enviar.")
            return

        # Verificar se há um DataFrame carregado
        if self.df is None:
            QMessageBox.warning(self, "Erro", "Nenhum dado carregado. Por favor, carregue um arquivo primeiro.")
            return

        # Criar um novo DataFrame apenas com os contatos selecionados
        selected_phones = [item.text(0) for item in selected_items]
        selected_df = self.df[self.df['telefone'].astype(str).isin(selected_phones)]

        if selected_df.empty:
            QMessageBox.warning(self, "Erro", "Não foi possível encontrar os contatos selecionados no DataFrame.")
            return

        # Confirmar o envio
        reply = QMessageBox.question(
            self,
            "Confirmar Envio",
            f"Deseja enviar mensagens para {len(selected_df)} contatos selecionados?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.send_button.setEnabled(False)
            self.progress.setValue(0)

            # Criar thread com o DataFrame filtrado
            thread = SendMessagesThread(selected_df, self.setup_driver)
            if self.mensagens_config:
                thread.MENSAGENS = self.mensagens_config.copy()

            self.thread = thread
            self.thread.progress_updated.connect(self.progress.setValue)
            self.thread.status_updated.connect(self.status_label.setText)
            self.thread.finished_signal.connect(self.sending_finished)
            self.thread.start()

    def export_whatsapp_contacts(self):
        """Exporta os contatos carregados para um arquivo Excel."""
        if self.df is None or self.df.empty:
            QMessageBox.warning(self, "Erro", "Nenhum contato carregado para exportar.")
            return

        # Solicitar local para salvar o arquivo
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exportar Contatos",
            "contatos_whatsapp.xlsx",
            "Excel files (*.xlsx)"
        )

        if not file_path:
            return

        try:
            # Criar uma cópia do DataFrame para exportar
            export_df = self.df.copy()

            # Adicionar colunas adicionais se existirem
            columns_to_export = ['telefone', 'tipo_mensagem']
            if 'horario_envio' in export_df.columns:
                columns_to_export.append('horario_envio')
            if 'nome' in export_df.columns:
                columns_to_export.append('nome')
            if 'endereco' in export_df.columns:
                columns_to_export.append('endereco')
            if 'website' in export_df.columns:
                columns_to_export.append('website')
            if 'categoria' in export_df.columns:
                columns_to_export.append('categoria')

            # Exportar apenas as colunas selecionadas
            export_df = export_df[columns_to_export]

            # Salvar o arquivo
            export_df.to_excel(file_path, index=False)

            QMessageBox.information(
                self,
                "Sucesso",
                f"Contatos exportados com sucesso para:\n{file_path}"
            )
        except Exception as e:
            QMessageBox.critical(self, "Erro", f"Erro ao exportar contatos: {str(e)}")

    def clear_gmaps_results(self):
        """Limpa os resultados da busca no Google Maps."""
        self.results_table.setRowCount(0)
        self.gmaps_status.setText("Resultados limpos.")

    def export_selected_results(self):
        """Exporta os resultados selecionados para um arquivo Excel ou CSV."""
        # Verificar se há linhas selecionadas
        selected_rows = set()
        for item in self.results_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "Aviso", "Por favor, selecione pelo menos um resultado para exportar.")
            return

        # Criar lista de negócios a partir das linhas selecionadas
        business_list = BusinessList()
        for row in selected_rows:
            business = Business()
            business.name = self.results_table.item(row, 0).text()
            business.address = self.results_table.item(row, 1).text()
            business.phone_number = self.results_table.item(row, 2).text()
            business.website = self.results_table.item(row, 3).text()
            business.rating = self.results_table.item(row, 4).text()
            business.category = self.results_table.item(row, 5).text()
            business_list.business_list.append(business)

        # Solicitar local para salvar o arquivo
        file_format = "excel" if self.excel_radio.isChecked() else "csv"
        file_extension = ".xlsx" if file_format == "excel" else ".csv"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Salvar Resultados Selecionados",
            f"resultados_selecionados{file_extension}",
            f"{'Excel' if file_format == 'excel' else 'CSV'} files (*{file_extension})"
        )

        if not file_path:
            return

        # Salvar o arquivo
        save_dir = str(Path(file_path).parent)
        filename = Path(file_path).stem

        if file_format == "excel":
            result = business_list.save_to_excel(filename, save_dir)
        else:
            result = business_list.save_to_csv(filename, save_dir)

        QMessageBox.information(self, "Sucesso", f"Resultados selecionados exportados com sucesso!\n\n{result}")

    def send_results_to_whatsapp(self):
        """Envia os resultados selecionados para a aba do WhatsApp."""
        # Verificar se há linhas selecionadas
        selected_rows = set()
        for item in self.results_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "Aviso", "Por favor, selecione pelo menos um resultado para enviar ao WhatsApp.")
            return

        # Criar DataFrame com os dados selecionados
        data = []
        for row in selected_rows:
            phone = self.results_table.item(row, 2).text()
            # Limpar o número de telefone (remover caracteres não numéricos)
            phone = ''.join(filter(str.isdigit, phone))
            if phone:  # Só adicionar se tiver um número de telefone
                data.append({
                    'telefone': phone,
                    'nome': self.results_table.item(row, 0).text(),
                    'endereco': self.results_table.item(row, 1).text(),
                    'website': self.results_table.item(row, 3).text(),
                    'categoria': self.results_table.item(row, 5).text()
                })

        if not data:
            QMessageBox.warning(self, "Aviso", "Nenhum dos resultados selecionados possui número de telefone válido.")
            return

        # Criar DataFrame
        self.df = pd.DataFrame(data)

        # Distribuir os tipos de mensagem
        self.distribuir_mensagens()

        # Atualizar a visualização
        self.update_preview()

        # Mudar para a tela do WhatsApp
        self.stacked_widget.setCurrentIndex(3)

        QMessageBox.information(
            self,
            "Sucesso",
            f"{len(data)} contatos foram carregados para envio de mensagens pelo WhatsApp.\n\n"
            "Você pode agora configurar as mensagens e iniciar o envio."
        )

    def show_continue_dialog(self, message, require_response):
        """Exibe um diálogo perguntando se deseja continuar a busca."""
        if require_response:
            # Usar a mensagem recebida do thread
            reply = QMessageBox.question(
                self,
                "Continuar Busca",
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.gmaps_thread.continue_response = "S"
            else:
                self.gmaps_thread.continue_response = "N"

    def load_specific_file(self, file_path):
        """Carrega um arquivo específico para envio de mensagens."""
        try:
            self.df = pd.read_excel(file_path)

            # Verificar se há coluna de telefone
            if 'phone_number' in self.df.columns:
                # Renomear coluna para o formato esperado
                self.df = self.df.rename(columns={'phone_number': 'telefone'})

            if 'telefone' not in self.df.columns:
                raise ValueError("O arquivo deve conter a coluna 'telefone'")

            # Distribuir os tipos de mensagem
            self.distribuir_mensagens()

            # Calcular a distribuição atual
            distribuicao = self.df['tipo_mensagem'].value_counts() if 'tipo_mensagem' in self.df.columns else {}

            # Formatar mensagem de sucesso
            msg = f"✅ Arquivo carregado com sucesso!\n\n• {len(self.df)} contatos encontrados"
            if distribuicao:
                msg += "\n• Distribuição de mensagens:\n"
                for tipo, count in distribuicao.items():
                    msg += f"  - Tipo {tipo}: {count} contatos ({count/len(self.df)*100:.1f}%)\n"

            self.update_preview()
            QMessageBox.information(self, "Sucesso", msg)
        except Exception as e:
            QMessageBox.critical(self, "Erro", f"Erro ao processar o arquivo: {str(e)}")

    def distribuir_mensagens(self):
        """Distribui os tipos de mensagem para os contatos."""
        if self.df is not None and 'tipo_mensagem' not in self.df.columns:
            # Distribuir os tipos de mensagem conforme os percentuais
            n_total = len(self.df)
            n_a = int(n_total * 0.5)  # 50% para A
            n_b = int(n_total * 0.25)  # 25% para B
            # O restante vai para C

            tipos = ['A'] * n_a + ['B'] * n_b + ['C'] * (n_total - n_a - n_b)
            random.shuffle(tipos)  # Embaralhar os tipos
            self.df['tipo_mensagem'] = tipos

    def apply_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f6fa;
            }

            #settingsFrame {
                background-color: white;
                border-radius: 10px;
                padding: 20px;
            }

            QPushButton {
                background-color: #25D366;  /* Cor verde do WhatsApp */
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #21c354;
            }

            QPushButton:pressed {
                background-color: #1da147;
            }

            #sendButton {
                background-color: #25D366;  /* Cor verde do WhatsApp */
                font-size: 16px;
                padding: 15px;
            }

            QTreeWidget {
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
                padding: 10px;
            }

            QTreeWidget::item {
                padding: 5px;
            }

            QProgressBar {
                border: none;
                border-radius: 5px;
                background-color: #dcdde1;
                height: 10px;
                text-align: center;
            }

            QProgressBar::chunk {
                background-color: #25D366;  /* Cor verde do WhatsApp */
                border-radius: 5px;
            }

            QSpinBox {
                padding: 5px;
                border: 1px solid #dcdde1;
                border-radius: 5px;
                background-color: white;
            }

            QLabel {
                color: #2d3436;
            }

            #statusLabel {
                color: #576574;
                font-size: 14px;
            }

        """)

        self.setWindowIcon(QIcon(str(PASTA_ICONES / "modal1.png")))

    def download_template(self):
        df = pd.DataFrame({
            'telefone': ['+5511999999999', '11999999999']
        })

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Salvar Template",
            "template_whats_invite_message.xlsx",
            "Excel files (*.xlsx)"
        )

        if file_path:
            df.to_excel(file_path, index=False)
            QMessageBox.information(self, "Sucesso",
                                  "Template baixado com sucesso!\n\n"
                                  "Instrução de uso:\n"
                                  "1. Preencha apenas a coluna 'telefone'\n"
                                  "2. A distribuição será feita automaticamente:\n"
                                  "   • 50% receberão mensagem tipo A (abordagem direta)\n"
                                  "   • 25% receberão mensagem tipo B (pergunta)\n"
                                  "   • 25% receberão mensagem tipo C (gancho de valor)\n\n"
                                  "Ao carregar o arquivo, você verá a distribuição exata das mensagens.")

    def load_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Abrir Arquivo",
            "",
            "Excel files (*.xlsx)"
        )

        if file_path:
            try:
                self.df = pd.read_excel(file_path)
                if 'telefone' not in self.df.columns:
                    raise ValueError("O arquivo deve conter a coluna 'telefone'")

                # Distribuir os tipos de mensagem
                self.distribuir_mensagens()

                # Calcular a distribuição atual
                distribuicao = self.df['tipo_mensagem'].value_counts()
                total = len(self.df)

                msg = (
                    "Arquivo carregado com sucesso!\n\n"
                    "Distribuição atual das mensagens:\n"
                    f"Tipo A (Abordagem direta): {distribuicao.get('A', 0)} contatos ({distribuicao.get('A', 0)/total*100:.1f}%)\n"
                    f"Tipo B (Pergunta): {distribuicao.get('B', 0)} contatos ({distribuicao.get('B', 0)/total*100:.1f}%)\n"
                    f"Tipo C (Gancho de valor): {distribuicao.get('C', 0)} contatos ({distribuicao.get('C', 0)/total*100:.1f}%)"
                )

                self.update_preview()
                QMessageBox.information(self, "Sucesso", msg)
            except Exception as e:
                QMessageBox.critical(self, "Erro", f"Erro ao processar o arquivo: {str(e)}")

    def update_preview(self):
        self.tree.clear()

        # Se não houver arquivo carregado, mostrar exemplos
        if self.df is None:
            mensagens = self.mensagens_config if self.mensagens_config else SendMessagesThread.MENSAGENS
            # Mostrar exemplo de cada tipo de mensagem
            for tipo in ['A', 'B', 'C']:
                mensagem = mensagens[tipo].format('1234')  # Exemplo com últimos 4 dígitos
                item = QTreeWidgetItem([
                    'Exemplo',
                    f"Tipo {tipo}: {mensagem}",
                    datetime.now().strftime('%H:%M'),
                    'Pendente'
                ])
                self.tree.addTopLevelItem(item)
            return

        # Se houver arquivo carregado, mostrar mensagens reais
        mensagens = self.mensagens_config if self.mensagens_config else SendMessagesThread.MENSAGENS
        for _, row in self.df.iterrows():
            tipo = row.get('tipo_mensagem', 'A')
            mensagem = mensagens[tipo].format(str(row['telefone'])[-4:])
            horario = row.get('horario_envio', '')
            horario_str = horario.strftime('%H:%M:%S') if horario else datetime.now().strftime('%H:%M')

            # Determinar o status da mensagem
            status = 'Pendente'
            if horario:
                status = 'Enviado'

            item = QTreeWidgetItem([
                str(row['telefone']),
                f"Tipo {tipo}: {mensagem}",
                horario_str,
                status
            ])

            # Definir cor de fundo com base no status
            if status == 'Enviado':
                for col in range(4):
                    item.setBackground(col, Qt.GlobalColor.green)

            self.tree.addTopLevelItem(item)

    def setup_driver(self):
        try:
            chrome_options = Options()

            # Configurações básicas de segurança
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-infobars')
            chrome_options.add_argument('--ignore-certificate-errors')

            # Desabilitar GPU e otimizar renderização
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--disable-features=VizDisplay')
            chrome_options.add_argument('--disable-webgl')
            chrome_options.add_argument('--disable-accelerated-2d-canvas')
            chrome_options.add_argument('--disable-accelerated-video-decode')
            chrome_options.add_argument('--disable-accelerated-video-encode')
            chrome_options.add_argument('--disable-gpu-compositing')
            chrome_options.add_argument('--disable-gpu-sandbox')
            chrome_options.add_argument('--disable-gpu-rasterization')

            # Otimizações de memória e performance
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-sync')
            chrome_options.add_argument('--disable-background-networking')
            chrome_options.add_argument('--disable-breakpad')
            chrome_options.add_argument('--disable-component-update')
            chrome_options.add_argument('--disable-domain-reliability')
            chrome_options.add_argument('--disable-features=TranslateUI')

            # Configurações de automação
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Configurar janela
            chrome_options.add_argument('--start-maximized')
            chrome_options.add_argument('--window-size=1920,1080')

            # Criar serviço e driver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Configurar timeouts
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)

            return driver

        except Exception as e:
            QMessageBox.critical(self, "Erro",
                             f"Erro ao inicializar o Chrome: {str(e)}\n"
                             "Verifique se o Google Chrome está instalado corretamente.")
            return None

    def start_sending(self):
        if self.df is None or self.df.empty:
            QMessageBox.warning(self, "Erro", "Por favor, carregue um arquivo com dados primeiro!")
            return

        self.send_button.setEnabled(False)
        self.progress.setValue(0)

        thread = SendMessagesThread(self.df, self.setup_driver)
        # Se houver mensagens configuradas, atualizar no thread
        if self.mensagens_config:
            thread.MENSAGENS = self.mensagens_config.copy()

        self.thread = thread
        self.thread.progress_updated.connect(self.progress.setValue)
        self.thread.status_updated.connect(self.status_label.setText)
        self.thread.finished_signal.connect(self.sending_finished)
        self.thread.start()

    def configurar_mensagens(self):
        dialog = ConfigMensagensDialog(self.mensagens_config, self)
        if dialog.exec():
            self.mensagens_config = dialog.get_mensagens()
            SendMessagesThread.MENSAGENS = self.mensagens_config.copy()  # Atualizar MENSAGENS da classe
            self.save_settings()  # Salvar automaticamente ao configurar
            self.update_preview()  # Atualizar preview com as novas mensagens
            QMessageBox.information(self, "Sucesso",
                                  "Mensagens configuradas com sucesso!\n\n"
                                  "Preview atualizado com as novas mensagens.")

    def sending_finished(self, messages_sent):
        self.send_button.setEnabled(True)
        if messages_sent > 0:
            # Criar relatório de envios
            relatorio_path = f"relatorio_envios_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            try:
                df_report = self.df[['telefone', 'tipo_mensagem', 'horario_envio']]
                df_report.columns = ['Telefone', 'Tipo de Mensagem', 'Horário de Envio']
                df_report.to_excel(relatorio_path, index=False)
                QMessageBox.information(self, "Sucesso",
                                      f"✨ Processo concluído!\n\n"
                                      f"• {messages_sent} mensagens enviadas\n"
                                      f"• Relatório salvo em: {relatorio_path}")
            except Exception as e:
                QMessageBox.information(self, "Sucesso",
                                      f"✨ Processo concluído! {messages_sent} mensagens enviadas.\n\n"
                                      f"Erro ao salvar relatório: {str(e)}")
        else:
            QMessageBox.critical(self, "Erro", "❌ Nenhuma mensagem foi enviada com sucesso.")

    def save_settings(self):
        settings = {
            'mensagens': self.mensagens_config
        }
        try:
            with open('mensagens_config.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            QMessageBox.warning(self, "Aviso", f"Erro ao salvar configurações: {str(e)}")

    def load_settings(self):
        try:
            with open('mensagens_config.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                self.mensagens_config = settings.get('mensagens', {})
                SendMessagesThread.MENSAGENS = self.mensagens_config.copy()  # Atualizar MENSAGENS da classe
                if self.mensagens_config:
                    QMessageBox.information(self, "Sucesso",
                                         "Mensagens carregadas:\n\n" +
                                         "\n".join(f"Tipo {tipo}: {msg[:50]}..."
                                                 for tipo, msg in self.mensagens_config.items()))
        except FileNotFoundError:
            SendMessagesThread.MENSAGENS = {
                'A': "Olá {}, tudo bem? Vi que você...",
                'B': "Oi {}, posso te fazer uma pergunta rápida?",
                'C': "Oi {}, tenho uma dica que pode te ajudar..."
            }
        except Exception as e:
            QMessageBox.warning(self, "Aviso", f"Erro ao carregar configurações: {str(e)}")

    def log_message(self, phone, message, status):
        with open("log.txt", "a") as log_file:
            log_file.write(f"{datetime.now()}: {status} - {phone}: {message}\n")

    def schedule_message(self, phone, message, time):
        schedule.every().day.at(time).do(self.send_invite_message, phone, message)

    def save_template(self):
        template = self.template_input.text()
        with open("templates.json", "a") as f:
            json.dump(template, f)

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Aplicar estilo e paleta de cores
    app.setStyle('Fusion')
    app.setPalette(get_application_palette())
    app.setStyleSheet(get_application_stylesheet())

    # Criar e exibir a janela principal
    window = WhatsInviteMessage()
    window.show()
    sys.exit(app.exec())
