"""
Interface da aba de busca automatizada para a versão desktop do PROSPECTO.
Design moderno com apenas cor azul nos botões.
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QProgressBar, QMessageBox, QTextEdit)
from PyQt6.QtGui import QIcon
import config_manager

from automated_search_desktop import AutomatedSearchThread
from desktop_theme import (
    PRIMARY_COLOR, CARD_COLOR, SUCCESS_COLOR, ERROR_COLOR, FONT_FAMILY
)


class AutomatedSearchTab(QWidget):
    """Aba de busca automatizada com design moderno."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.search_thread = None

        # Carregar configurações
        self.config = config_manager.load_config()

        self.initUI()

    def initUI(self):
        """Inicializa a interface da aba com design moderno."""
        # Layout principal com margens responsivas
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(24)
        main_layout.setContentsMargins(32, 32, 32, 32)

        # Header Section - Hero Area
        self.create_header_section(main_layout)

        # Dashboard Cards Section
        self.create_dashboard_section(main_layout)

        # Control Panel Section
        self.create_control_panel(main_layout)

        # Logs Section
        self.create_logs_section(main_layout)

        # Atualizar status das configurações
        self.update_config_status()

    def create_header_section(self, parent_layout):
        """Cria a seção de cabeçalho com design hero."""
        # Container principal do header
        header_container = QWidget()
        header_container.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_COLOR};
                border-radius: 20px;
                padding: 32px;
            }}
        """)

        header_layout = QVBoxLayout(header_container)
        header_layout.setSpacing(16)

        # Título principal
        title_label = QLabel("🚀 Busca Automatizada")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                font-size: 32px;
                font-weight: 800;
                font-family: {FONT_FAMILY};
                margin: 0;
                padding: 0;
            }}
        """)
        header_layout.addWidget(title_label)

        # Subtítulo
        subtitle_label = QLabel("Automatize suas buscas no Google Maps com inteligência artificial")
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: rgba(255, 255, 255, 0.9);
                font-size: 16px;
                font-weight: 400;
                font-family: {FONT_FAMILY};
                margin: 0;
                padding: 0;
            }}
        """)
        header_layout.addWidget(subtitle_label)

        # Botão SETUP com design especial
        setup_button_container = QWidget()
        setup_button_layout = QHBoxLayout(setup_button_container)
        setup_button_layout.setContentsMargins(0, 16, 0, 0)

        self.setup_button = QPushButton("⚙️ CONFIGURAR SETUP")
        self.setup_button.setStyleSheet(f"""
            QPushButton {{
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 16px 32px;
                font-weight: 700;
                font-size: 16px;
                font-family: {FONT_FAMILY};
                min-height: 20px;
            }}
            QPushButton:hover {{
                background: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.5);
            }}
            QPushButton:pressed {{
                background: rgba(255, 255, 255, 0.1);
            }}
        """)
        self.setup_button.clicked.connect(self.open_setup)

        setup_button_layout.addWidget(self.setup_button)
        setup_button_layout.addStretch()
        header_layout.addWidget(setup_button_container)

        parent_layout.addWidget(header_container)

    def create_dashboard_section(self, parent_layout):
        """Cria a seção de dashboard com cards informativos."""
        # Container para os cards
        dashboard_container = QWidget()
        dashboard_layout = QHBoxLayout(dashboard_container)
        dashboard_layout.setSpacing(20)

        # Card de Status das Configurações
        self.config_card = self.create_info_card(
            "⚙️", "Configurações", "Não definidas", ERROR_COLOR
        )
        dashboard_layout.addWidget(self.config_card)

        # Card de Status da Execução
        self.status_card = self.create_info_card(
            "🎯", "Status", "Pronto para iniciar", SUCCESS_COLOR
        )
        dashboard_layout.addWidget(self.status_card)

        # Card de Progresso
        self.progress_card = self.create_progress_card()
        dashboard_layout.addWidget(self.progress_card)

        parent_layout.addWidget(dashboard_container)

    def create_info_card(self, icon, title, subtitle, color):
        """Cria um card informativo."""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background-color: {CARD_COLOR};
                border-radius: 16px;
                border: 1px solid #E0E0E0;
                padding: 24px;
            }}
            QWidget:hover {{
                border: 1px solid {PRIMARY_COLOR};
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(12)

        # Header do card
        header_layout = QHBoxLayout()

        # Ícone
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {color};
                background: rgba(0, 112, 243, 0.1);
                border-radius: 12px;
                padding: 8px;
                min-width: 40px;
                max-width: 40px;
                min-height: 40px;
                max-height: 40px;
                text-align: center;
            }}
        """)
        header_layout.addWidget(icon_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Título
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: 600;
                color: #2C3E50;
                margin: 0;
            }}
        """)
        layout.addWidget(title_label)

        # Subtítulo (será atualizado dinamicamente)
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {color};
                font-weight: 500;
                margin: 0;
            }}
        """)
        layout.addWidget(subtitle_label)

        # Armazenar referências para atualização
        card.title_label = title_label
        card.subtitle_label = subtitle_label

        return card

    def create_progress_card(self):
        """Cria o card de progresso."""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background-color: {CARD_COLOR};
                border-radius: 16px;
                border: 1px solid #E0E0E0;
                padding: 24px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(12)

        # Header do card
        header_layout = QHBoxLayout()

        # Ícone
        icon_label = QLabel("📊")
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {PRIMARY_COLOR};
                background: rgba(0, 112, 243, 0.1);
                border-radius: 12px;
                padding: 8px;
                min-width: 40px;
                max-width: 40px;
                min-height: 40px;
                max-height: 40px;
                text-align: center;
            }}
        """)
        header_layout.addWidget(icon_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Título
        title_label = QLabel("Progresso")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: 600;
                color: #2C3E50;
                margin: 0;
            }}
        """)
        layout.addWidget(title_label)

        # Barra de progresso moderna
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 8px;
                background-color: #F0F0F0;
                text-align: center;
                height: 16px;
                font-size: 12px;
                font-weight: 600;
                color: white;
            }}
            QProgressBar::chunk {{
                background-color: {PRIMARY_COLOR};
                border-radius: 8px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # Percentual
        self.progress_label = QLabel("0%")
        self.progress_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {PRIMARY_COLOR};
                font-weight: 600;
                margin: 0;
            }}
        """)
        layout.addWidget(self.progress_label)

        return card

    def create_control_panel(self, parent_layout):
        """Cria o painel de controle moderno."""
        # Container principal
        control_container = QWidget()
        control_container.setStyleSheet(f"""
            QWidget {{
                background-color: {CARD_COLOR};
                border-radius: 20px;
                border: 1px solid #E0E0E0;
                padding: 32px;
            }}
        """)

        control_layout = QVBoxLayout(control_container)
        control_layout.setSpacing(24)

        # Título da seção
        title_label = QLabel("🎮 Painel de Controle")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: 700;
                color: #2C3E50;
                margin-bottom: 8px;
            }}
        """)
        control_layout.addWidget(title_label)

        # Descrição
        desc_label = QLabel("Controle a execução das buscas automatizadas")
        desc_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: #7F8C8D;
                margin-bottom: 16px;
            }}
        """)
        control_layout.addWidget(desc_label)

        # Botões de controle
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(16)

        # Botão Iniciar
        self.start_button = QPushButton("▶️ Iniciar Buscas")
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {PRIMARY_COLOR};
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-weight: 600;
                font-size: 14px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: #0056CC;
            }}
            QPushButton:pressed {{
                background-color: #003D99;
            }}
            QPushButton:disabled {{
                background-color: #BDBDBD;
                color: #9E9E9E;
            }}
        """)
        self.start_button.clicked.connect(self.start_automated_search)

        # Botão Pausar
        self.pause_button = QPushButton("⏸️ Pausar")
        self.pause_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-weight: 600;
                font-size: 14px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: #F57C00;
            }}
            QPushButton:pressed {{
                background-color: #E65100;
            }}
            QPushButton:disabled {{
                background-color: #BDBDBD;
                color: #9E9E9E;
            }}
        """)
        self.pause_button.clicked.connect(self.pause_automated_search)
        self.pause_button.setEnabled(False)

        # Botão Parar
        self.stop_button = QPushButton("⏹️ Parar")
        self.stop_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #F44336;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-weight: 600;
                font-size: 14px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: #D32F2F;
            }}
            QPushButton:pressed {{
                background-color: #C62828;
            }}
            QPushButton:disabled {{
                background-color: #BDBDBD;
                color: #9E9E9E;
            }}
        """)
        self.stop_button.clicked.connect(self.stop_automated_search)
        self.stop_button.setEnabled(False)

        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.pause_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addStretch()

        control_layout.addLayout(buttons_layout)

        # Status atual
        self.status_label = QLabel("Pronto para iniciar buscas automatizadas")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: #7F8C8D;
                background-color: #F8F9FA;
                border-radius: 8px;
                padding: 12px 16px;
                margin-top: 16px;
            }}
        """)
        control_layout.addWidget(self.status_label)

        parent_layout.addWidget(control_container)

    def create_logs_section(self, parent_layout):
        """Cria a seção de logs moderna."""
        # Container principal dos logs
        logs_container = QWidget()
        logs_container.setStyleSheet(f"""
            QWidget {{
                background-color: {CARD_COLOR};
                border-radius: 20px;
                border: 1px solid #E0E0E0;
                padding: 32px;
            }}
        """)

        logs_layout = QVBoxLayout(logs_container)
        logs_layout.setSpacing(20)

        # Header da seção
        header_layout = QHBoxLayout()

        # Título
        title_label = QLabel("📋 Logs de Execução")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: 700;
                color: #2C3E50;
                margin: 0;
            }}
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Botão limpar logs
        clear_logs_button = QPushButton("🗑️ Limpar")
        clear_logs_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: #C0392B;
            }}
            QPushButton:pressed {{
                background-color: #A93226;
            }}
        """)
        clear_logs_button.clicked.connect(self.clear_logs)
        header_layout.addWidget(clear_logs_button)

        logs_layout.addLayout(header_layout)

        # Área de logs moderna
        self.logs_area = QTextEdit()
        self.logs_area.setReadOnly(True)
        self.logs_area.setMinimumHeight(200)
        self.logs_area.setMaximumHeight(300)
        self.logs_area.setStyleSheet(f"""
            QTextEdit {{
                background-color: #1E1E1E;
                color: #E0E0E0;
                border: 1px solid #404040;
                border-radius: 12px;
                padding: 16px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 13px;
                line-height: 1.4;
            }}
            QScrollBar:vertical {{
                background-color: #2D2D2D;
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #777777;
            }}
        """)
        logs_layout.addWidget(self.logs_area)

        parent_layout.addWidget(logs_container)

    def open_setup(self):
        """Abre a tela de SETUP."""
        from setup_screen import SetupScreen

        # Criar a tela de setup
        setup_screen = SetupScreen(self.parent)

        # Conectar o sinal de configuração salva
        setup_screen.config_saved.connect(self.update_config_status)

        # Adicionar a tela ao stack widget
        if hasattr(self.parent, 'stacked_widget'):
            self.parent.stacked_widget.addWidget(setup_screen)
            self.parent.stacked_widget.setCurrentWidget(setup_screen)

    def update_config_status(self):
        """Atualiza o status das configurações."""
        # Recarregar configurações
        self.config = config_manager.load_config()

        queries = self.config.get("search_queries", [])
        save_dir = self.config.get("default_save_dir", "")

        if queries and save_dir:
            # Atualizar card de configurações
            self.config_card.subtitle_label.setText(f"{len(queries)} consultas configuradas")
            self.config_card.subtitle_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 14px;
                    color: {SUCCESS_COLOR};
                    font-weight: 500;
                    margin: 0;
                }}
            """)
        else:
            # Atualizar card de configurações
            self.config_card.subtitle_label.setText("Não definidas")
            self.config_card.subtitle_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 14px;
                    color: {ERROR_COLOR};
                    font-weight: 500;
                    margin: 0;
                }}
            """)

    def add_log(self, message):
        """Adiciona uma mensagem aos logs."""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs_area.append(log_entry)

        # Auto-scroll para o final
        cursor = self.logs_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.logs_area.setTextCursor(cursor)

    def clear_logs(self):
        """Limpa a área de logs."""
        self.logs_area.clear()
        self.add_log("Logs limpos")

    def update_status_and_log(self, message):
        """Atualiza o status e adiciona ao log."""
        self.status_label.setText(message)

        # Atualizar card de status
        self.status_card.subtitle_label.setText(message)

        self.add_log(message)

    def update_progress(self, value):
        """Atualiza a barra de progresso e o card."""
        self.progress_bar.setValue(value)
        self.progress_label.setText(f"{value}%")

    def start_automated_search(self):
        """Inicia a busca automatizada."""
        # Recarregar configurações
        self.config = config_manager.load_config()

        # Validar configurações
        queries = self.config.get("search_queries", [])
        save_dir = self.config.get("default_save_dir", "")

        if not queries:
            QMessageBox.warning(self, "Erro", "Nenhuma consulta de busca configurada. Clique em SETUP para configurar.")
            return

        if not save_dir:
            QMessageBox.warning(self, "Erro", "Diretório de salvamento não configurado. Clique em SETUP para configurar.")
            return

        # Adicionar log de início
        self.add_log(f"Iniciando busca automatizada com {len(queries)} consultas")

        # Atualizar UI
        self.progress_bar.setValue(0)
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)

        # Obter configurações
        file_format = self.config.get("default_export_format", "excel")
        headless_mode = self.config.get("headless_mode", True)

        # Criar e iniciar thread de busca
        self.search_thread = AutomatedSearchThread(
            search_queries=queries,
            save_dir=save_dir,
            file_format=file_format,
            headless_mode=headless_mode
        )

        # Conectar sinais
        self.search_thread.progress_updated.connect(self.update_progress)
        self.search_thread.status_updated.connect(self.update_status_and_log)
        self.search_thread.finished_signal.connect(self.search_finished)
        self.search_thread.continue_query.connect(self.show_continue_dialog)
        self.search_thread.query_completed.connect(self.query_completed)

        # Iniciar thread
        self.search_thread.start()
        self.status_label.setText("Iniciando buscas automatizadas...")
        self.add_log("Thread de busca iniciada")

    def pause_automated_search(self):
        """Pausa ou retoma a busca automatizada."""
        if not self.search_thread:
            return

        if self.search_thread.paused:
            # Retomar
            self.search_thread.resume()
            self.pause_button.setText("⏸️ Pausar")
            self.status_label.setText("Buscas automatizadas retomadas")
            self.add_log("Busca retomada pelo usuário")
        else:
            # Pausar
            self.search_thread.pause()
            self.pause_button.setText("▶️ Continuar")
            self.status_label.setText("Buscas automatizadas pausadas")
            self.add_log("Busca pausada pelo usuário")

    def stop_automated_search(self):
        """Para a busca automatizada."""
        if not self.search_thread:
            return

        reply = QMessageBox.question(
            self,
            "Confirmar",
            "Tem certeza que deseja interromper as buscas automatizadas?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.add_log("Parando busca automatizada...")
            self.search_thread.stop()
            self.search_thread = None

            # Atualizar UI
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.pause_button.setText("⏸️ Pausar")
            self.status_label.setText("Buscas automatizadas interrompidas")
            self.add_log("Busca interrompida pelo usuário")

    def search_finished(self, result):
        """Callback chamado quando a busca automatizada é finalizada."""
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)

        if "ERRO" not in result:
            self.add_log(f"Busca finalizada com sucesso: {result}")
            QMessageBox.information(self, "Sucesso", result)
        else:
            self.add_log(f"Erro na busca: {result}")
            QMessageBox.warning(self, "Erro", result)

    def query_completed(self, index):
        """Callback chamado quando uma consulta é concluída."""
        # Adicionar log da consulta concluída
        self.config = config_manager.load_config()
        queries = self.config.get("search_queries", [])

        if index < len(queries):
            query_name = queries[index]
            self.add_log(f"Consulta {index + 1} concluída: {query_name}")

    def show_continue_dialog(self, message, require_response):
        """Exibe um diálogo perguntando se deseja continuar a busca."""
        if require_response:
            reply = QMessageBox.question(
                self,
                "Continuar Busca",
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.search_thread.continue_response = "S"
            else:
                self.search_thread.continue_response = "N"